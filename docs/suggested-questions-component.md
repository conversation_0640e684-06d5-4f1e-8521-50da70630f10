# 建议问题组件使用说明

## 概述

建议问题组件已经集成到 `MessageSender.vue` 中，用于在对话完成后显示建议问题的胶囊样式列表。

## 功能特点

### 🎨 设计特点
- **胶囊样式**：使用圆角胶囊样式，节省空间
- **自动换行**：支持多行显示，自动换行布局
- **响应式设计**：适配不同屏幕尺寸
- **触觉反馈**：点击时提供轻微震动反馈

### 🔧 技术实现
- 集成在 `MessageSender.vue` 组件中
- 通过 props 接收 `messagesSuggested` 数组
- 通过 `selectQuestion` 事件发送选中的问题
- 自动在对话完成后显示

## 使用方式

### 1. 组件集成

```vue
<MessageSender 
  :messagesSuggested="messagesSuggested" 
  @selectQuestion="handleQuestionSelect"
  @send="sendMessage" 
  @setHeight="setSendHeight" 
/>
```

### 2. 事件处理

```typescript
// 处理建议问题点击
function handleQuestionSelect(question: string) {
  // 发送选中的建议问题
  sendMessage(question)
  // 清空建议问题列表
  difyStore.clearMessagesSuggested()
}
```

### 3. 自动加载机制

建议问题会在以下情况自动加载：
- AI 完成回复后
- 调用 `handleResponse` 方法时
- 有有效的 `message_id` 时

## 样式说明

### 胶囊样式特点
- 圆角边框：`rounded-full`
- 灰色背景：`bg-gray-100`
- 边框：`border: 1px solid #E5E7EB`
- 内边距：`px-3 py-1.5`
- 最大宽度：`calc(100% - 0.5rem)`

### 交互效果
- 点击时缩放：`transform: scale(0.95)`
- 背景变色：`bg-blue-50`
- 边框高亮：`border-color: #3B82F6`

### 文本处理
- 自动换行：`word-break: break-word`
- 最多显示2行：`-webkit-line-clamp: 2`
- 超出省略：`overflow: hidden`

## 数据流程

1. **加载建议问题**
   ```
   AI回复完成 → handleResponse → loadMessagesSuggested → 更新 messagesSuggested
   ```

2. **显示建议问题**
   ```
   messagesSuggested 有数据 → MessageSender 显示胶囊列表
   ```

3. **点击建议问题**
   ```
   用户点击 → handleQuestionClick → emit selectQuestion → 发送消息 → 清空列表
   ```

## 兼容性

- ✅ 微信小程序
- ✅ H5 平台
- ✅ UniApp 框架
- ✅ Vue 3 Composition API
- ✅ TypeScript 支持

## 注意事项

1. **性能优化**：建议问题列表会在用户点击后自动清空，避免重复显示
2. **用户体验**：提供触觉反馈，增强交互体验
3. **响应式设计**：在不同屏幕尺寸下都能正常显示
4. **文本处理**：长文本会自动换行，超长文本会被截断

## 示例效果

```
┌─────────────────────────────────────┐
│ [如何使用这个功能？] [更多详细信息]     │
│ [有什么注意事项吗？] [下一步怎么做？]   │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 输入消息...                      │ │
│ │                           [发送] │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## 更新日志

- **v1.0.0**: 初始版本，支持胶囊样式建议问题显示
- 集成到 MessageSender 组件中
- 支持自动加载和点击发送功能
- 提供完整的用户交互体验
