<script setup lang="ts">
import { computed, ref } from 'vue'

// 用户数据
const balance = ref(120)
const selectedOption = ref('1') // 默认选中第一个选项
const selectedMethod = ref('wechat')
const isLoading = ref(false)
const showSuccess = ref(false)

// 充值选项数据
const rechargeOptions = [
  { id: '1', amount: 100, price: 10, bonus: 0 },
  { id: '2', amount: 500, price: 45, bonus: 50 },
  { id: '3', amount: 1000, price: 88, bonus: 120 },
  { id: '4', amount: 2000, price: 168, bonus: 300 },
  { id: '5', amount: 5000, price: 200, bonus: 400 },
]

// 支付方式数据
const paymentMethods = [
  { id: 'wechat', name: '微信支付', icon: 'weixin', iconColor: '#22C55E' },
  // 可以添加更多支付方式
]

// 计算当前选中的价格
const selectedOptionPrice = computed(() => {
  const option = rechargeOptions.find(opt => opt.id === selectedOption.value)
  return option ? option.price : 0
})

// 计算当前选中的电力值
const selectedOptionAmount = computed(() => {
  const option = rechargeOptions.find(opt => opt.id === selectedOption.value)
  return option ? option.amount : 0
})

// 充值处理函数
async function handleRecharge() {
  if (isLoading.value) { return }

  try {
    isLoading.value = true

    // 模拟支付请求
    // eslint-disable-next-line no-console
    console.log('充值金额:', selectedOptionPrice.value)
    // eslint-disable-next-line no-console
    console.log('支付方式:', selectedMethod.value)

    // 模拟网络请求延迟
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 支付成功后更新余额
    balance.value += selectedOptionAmount.value
    showSuccess.value = true

    // 3秒后隐藏成功提示
    setTimeout(() => {
      showSuccess.value = false
    }, 3000)
  }
  catch (_error) {
    // 处理支付错误
    uni.showToast({
      title: '支付失败，请重试',
      icon: 'none',
    })
  }
  finally {
    isLoading.value = false
  }
}
</script>

<template>
  <view class="recharge-container">
    <scroll-view class="recharge-page" scroll-y>
      <!-- 余额卡片 -->
      <view class="balance-card">
        <view class="balance-card-content">
          <view class="balance-title">
            当前电力值
          </view>
          <view class="balance-amount">
            {{ balance }}
          </view>
          <view class="balance-info">
            <uni-icons type="info" size="14" color="#93C5FD" />
            <text class="ml-1">可用于智能体对话和创建</text>
          </view>
        </view>
        <view class="balance-decoration">
          <view class="balance-circle-1" />
          <view class="balance-circle-2" />
          <view class="balance-circle-3" />
          <view class="balance-circle-4" />
        </view>
      </view>

      <!-- 充值选项 -->
      <view class="recharge-options">
        <view class="section-title">
          <view class="section-title-icon" />
          <text>选择充值金额</text>
        </view>
        <view class="options-grid">
          <view
            v-for="option in rechargeOptions" :key="option.id" class="option-card"
            :class="{ selected: selectedOption === option.id }" @click="selectedOption = option.id"
          >
            <view class="option-content">
              <view class="option-amount">
                {{ option.amount }}
                <text class="option-unit">电力值</text>
              </view>
              <view class="option-price">
                ¥{{ option.price }}
              </view>
              <view v-if="option.bonus" class="option-bonus">
                <view class="bonus-tag">
                  赠送
                </view>
                <text>{{ option.bonus }}电力值</text>
              </view>
            </view>
            <view v-if="selectedOption === option.id" class="option-selected-mark">
              <uni-icons type="checkmarkempty" size="16" color="#FFFFFF" />
            </view>
            <view v-if="option.bonus" class="best-value-tag">
              超值
            </view>
          </view>
        </view>
      </view>

      <!-- 支付方式 -->
      <view class="payment-methods">
        <view class="section-title">
          <view class="section-title-icon" />
          <text>支付方式</text>
        </view>
        <view class="method-list">
          <view
            v-for="method in paymentMethods" :key="method.id" class="method-item"
            :class="{ selected: selectedMethod === method.id }" @click="selectedMethod = method.id"
          >
            <view class="method-icon-container">
              <uni-icons :type="method.icon" size="26" :color="method.iconColor" />
            </view>
            <view class="method-name">
              {{ method.name }}
            </view>
            <view v-if="selectedMethod === method.id" class="method-selected">
              <uni-icons type="checkbox-filled" size="22" color="#3B82F6" />
            </view>
            <view v-else class="method-unselected">
              <uni-icons type="circle" size="22" color="#CBD5E1" />
            </view>
          </view>
        </view>
      </view>

      <!-- 底部空白区域 -->
      <view class="bottom-space" />
    </scroll-view>

    <!-- 成功提示 -->
    <view v-if="showSuccess" class="success-notification">
      <uni-icons type="checkmarkempty" size="20" color="#3B82F6" />
      <text class="success-text">充值成功！</text>
    </view>

    <!-- 底部支付按钮 -->
    <view class="submit-button-container">
      <button class="submit-button" :class="{ loading: isLoading }" @click="handleRecharge">
        <view v-if="isLoading" class="loading-spinner" />
        <text v-else>确认支付 <text class="font-bold">¥{{ selectedOptionPrice }}</text></text>
      </button>
    </view>
  </view>
</template>

<style scoped>
.recharge-container {
  @apply flex  h-screen relative;
  background: linear-gradient(180deg, #eff6ff 0%, #f8fafc 100%);
}

.recharge-page {
  @apply flex-1 pb-16;
  background: transparent;
}

/* 余额卡片样式 */
.balance-card {
  @apply relative mx-5 my-3 rounded-2xl overflow-hidden;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  box-shadow:
    0 15px 25px -5px rgba(59, 130, 246, 0.3),
    0 8px 10px -6px rgba(59, 130, 246, 0.2);
}

.balance-card-content {
  @apply p-7 relative z-10;
}

.balance-title {
  @apply text-sm text-blue-100 mb-2 font-medium tracking-wide;
}

.balance-amount {
  @apply text-5xl font-bold text-white mb-3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.balance-info {
  @apply flex items-center text-xs text-blue-100;
}

.balance-decoration {
  @apply absolute inset-0 overflow-hidden;
  z-index: 1;
}

.balance-circle-1 {
  @apply absolute rounded-full bg-white opacity-10;
  width: 180px;
  height: 180px;
  top: -90px;
  right: -60px;
}

.balance-circle-2 {
  @apply absolute rounded-full bg-white opacity-5;
  width: 220px;
  height: 220px;
  bottom: -110px;
  left: -70px;
}

/* 添加更多装饰元素 */
.balance-circle-3 {
  @apply absolute rounded-full bg-white opacity-5;
  width: 60px;
  height: 60px;
  top: 20px;
  left: 60%;
}

.balance-circle-4 {
  @apply absolute rounded-full bg-white opacity-10;
  width: 15px;
  height: 15px;
  bottom: 30px;
  right: 30%;
}

/* 充值选项样式 */
.recharge-options {
  @apply px-5 py-2;
}

.section-title {
  @apply flex items-center text-base font-medium text-blue-900 mb-2;
}

.section-title-icon {
  @apply w-1 h-5 bg-blue-600 rounded-full mr-2;
}

.options-grid {
  @apply grid grid-cols-2 gap-4;
}

.option-card {
  @apply relative bg-white p-5 rounded-xl border border-gray-100 transition-all duration-300;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.03),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
}

.option-card:active {
  @apply transform scale-[0.98];
}

.option-card.selected {
  @apply border-blue-500 bg-blue-50/50;
  box-shadow:
    0 8px 15px -3px rgba(59, 130, 246, 0.15),
    0 4px 6px -2px rgba(59, 130, 246, 0.05);
  transform: translateY(-2px);
}

.option-content {
  @apply flex flex-col;
}

.option-amount {
  @apply text-xl font-bold text-gray-800 mb-1;
}

.option-unit {
  @apply text-sm font-normal text-gray-500 ml-1;
}

.option-price {
  @apply text-lg font-bold text-blue-600 mb-1;
}

.option-bonus {
  @apply flex items-center text-xs text-rose-500 mt-1;
}

.bonus-tag {
  @apply px-1.5 py-0.5 bg-rose-100 text-rose-500 rounded text-xs mr-1 font-medium;
}

.option-selected-mark {
  @apply absolute top-3 right-3 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center shadow-md;
}

.best-value-tag {
  @apply absolute top-0 left-0 bg-blue-600 text-white text-xs font-medium py-1 px-2 rounded-tl-xl rounded-br-xl;
}

.bottom-space {
  @apply h-20;
}

/* 支付方式样式 */
.payment-methods {
  @apply px-5 py-4;
}

.method-list {
  @apply bg-white rounded-xl overflow-hidden;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.03),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
}

.method-item {
  @apply flex items-center p-3 border-b border-gray-100 transition-colors duration-200;
}

.method-item:last-child {
  @apply border-b-0;
}

.method-item.selected {
  @apply bg-blue-50;
}

.method-icon-container {
  @apply w-12 h-12 rounded-full bg-blue-100/50 flex items-center justify-center mr-4 transition-transform duration-200;
}

.method-item:active .method-icon-container {
  @apply transform scale-95;
}

.method-name {
  @apply flex-1 text-base text-gray-800 font-medium;
}

.method-selected,
.method-unselected {
  @apply flex items-center justify-center;
}

/* 底部按钮样式 */
.submit-button-container {
  @apply fixed bottom-0 left-0 right-0 p-5 bg-white border-t border-gray-100;
  box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.03);
}

.submit-button {
  @apply w-full py-4 text-white font-medium rounded-xl text-base transition-all duration-300;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  box-shadow:
    0 10px 15px -3px rgba(59, 130, 246, 0.25),
    0 4px 6px -2px rgba(59, 130, 246, 0.1);
}

.submit-button:active {
  transform: translateY(2px);
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2);
}

/* 加载状态样式 */
.submit-button.loading {
  @apply opacity-90;
}

.loading-spinner {
  @apply w-6 h-6 border border-white border-t-transparent rounded-full inline-block;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 成功提示样式 */
.success-notification {
  @apply absolute top-24 left-0 right-0 mx-auto w-4/5 max-w-xs bg-blue-50 border border-blue-200 rounded-xl p-4 flex items-center justify-center z-50;
  animation:
    slideDown 0.3s ease-out,
    fadeOut 0.3s ease-out 2.7s;
  box-shadow:
    0 10px 15px -3px rgba(59, 130, 246, 0.1),
    0 4px 6px -2px rgba(59, 130, 246, 0.05);
}

.success-text {
  @apply ml-2 text-blue-800 font-medium;
}

@keyframes slideDown {
  0% {
    transform: translateY(-20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>
