<script setup lang="ts">
import { supabase } from '@/services/supabase'
import { useAuthStore } from '@/stores/auth'
import { useInviteLinkStore } from '@/stores/workspace'
import { ref } from 'vue'

// 获取用户信息和邀请链接相关功能
const inviteLinkStore = useInviteLinkStore()
const authStore = useAuthStore()
const { createCompanyByInviteLink } = inviteLinkStore

// 表单状态
const isLoading = ref(false)
const formError = ref('')
const formSuccess = ref(false)
const { user } = storeToRefs(authStore)

// 表单数据
const form = ref({
  name: '',
  icon: '',
  description: '',
  inviteCode: '',
})

// 图标上传相关
const iconPreview = ref('')

// 验证表单
function validateForm() {
  if (!form.value.name.trim()) {
    formError.value = '请输入空间名称'
    return false
  }

  if (!form.value.description.trim()) {
    formError.value = '请输入空间简介'
    return false
  }
  if (!form.value.icon.trim()) {
    formError.value = '请上传空间图标'
    return false
  }
  if (!form.value.inviteCode.trim()) {
    formError.value = '请输入邀请码'
    return false
  }

  return true
}

// 上传图标
function chooseIcon() {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: async (res) => {
      console.log(res, 'resssssss')

      const fileExt = res.tempFiles[0].path.split('.').pop()
      const fileName = `${Date.now()}.${fileExt}`
      const filePath = `icon/${fileName}`
      let fileF = { tempFilePath: res.tempFiles[0].path }
      console.log(fileF, 'resresresresresres', res.tempFiles[0])
      const { data, error: uploadError } = await supabase.storage.from('icon').upload(filePath, fileF)
      if (uploadError) {
        throw uploadError
      }
      iconPreview.value = `${import.meta.env.VITE_STORAGE}/icon/${data.path}`
      form.value.icon = iconPreview.value
    },
  })
}

// 创建工作空间
async function createWorkspace() {
  // 验证表单
  if (!validateForm()) {
    uni.showToast({
      title: formError.value,
      icon: 'none',
      duration: 2000,
    })
    return
  }

  try {
    isLoading.value = true

    // 显示加载中
    uni.showLoading({ title: '正在创建...' })

    // 调用创建空间的API
    await createCompanyByInviteLink(
      form.value.inviteCode,
      form.value.name,
      form.value.icon,
      form.value.description,
    )

    // 如果创建成功，可以进一步更新空间的图标和简介
    // 这里需要根据实际API调用更新空间信息

    // 模拟创建成功
    formSuccess.value = true

    // 显示成功提示
    uni.hideLoading()
    uni.showToast({
      title: '工作空间创建成功',
      icon: 'success',
      duration: 2000,
    })

    await authStore.checkUserInfo(user.value.id)
    // 创建成功后跳转到首页
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index',
      })
    }, 2000)
  }
  catch (error: any) {
    uni.hideLoading()
    formError.value = error.message || '创建失败，请重试'
    uni.showToast({
      title: formError.value,
      icon: 'none',
      duration: 2000,
    })
  }
  finally {
    isLoading.value = false
  }
}
</script>

<template>
  <view class="create-workspace-page">
    <!-- 页面背景装饰 -->
    <view class="page-decoration">
      <view class="decoration-circle-1" />
      <view class="decoration-circle-2" />
    </view>

    <!-- 表单内容区 -->
    <view class="form-container">
      <view class="form-header">
        <view class="form-title">
          创建您的工作空间
        </view>
        <view class="form-subtitle">
          通过邀请码创建专属于您的工作空间
        </view>
      </view>

      <!-- 基本信息区域 -->
      <view class="form-section">
        <view class="section-title">
          基本信息
        </view>

        <view class="form-group">
          <label class="form-label">空间名称</label>
          <view class="input-container">
            <uni-icons type="home" size="18" color="#3B82F6" class="input-icon" />
            <input v-model="form.name" class="form-input" placeholder="请输入空间名称">
          </view>
        </view>

        <view class="form-group">
          <label class="form-label">空间图标</label>
          <view class="avatar-upload-container">
            <view class="avatar-preview" @click="chooseIcon">
              <image v-if="iconPreview" :src="iconPreview" class="avatar-image" mode="aspectFill" />
              <view v-else class="avatar-placeholder">
                <uni-icons type="camera" size="24" color="#3B82F6" />
                <text class="upload-text">上传图标</text>
              </view>
            </view>
            <text class="avatar-tip">点击上传空间图标</text>
          </view>
        </view>

        <view class="form-group">
          <label class="form-label">空间简介</label>
          <view class="input-container">
            <uni-icons type="info" size="18" color="#3B82F6" class="input-icon" />
            <input v-model="form.description" class="form-input" placeholder="请输入空间简介">
          </view>
        </view>
      </view>

      <!-- 邀请码区域 -->
      <view class="form-section">
        <view class="section-title">
          邀请码
        </view>

        <view class="form-group">
          <label class="form-label">输入邀请码</label>
          <view class="input-container">
            <uni-icons type="paperplane" size="18" color="#3B82F6" class="input-icon" />
            <input v-model="form.inviteCode" class="form-input" placeholder="请输入邀请码">
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮区域 -->
    <view class="submit-button-container">
      <button class="submit-button" :disabled="isLoading" @click="createWorkspace">
        <uni-icons type="plusempty" size="18" color="#FFFFFF" />
        <text class="button-text">创建工作空间</text>
      </button>
    </view>
  </view>
</template>

<style scoped>
/* 页面整体样式 */
.create-workspace-page {
  @apply pb-24 min-h-screen relative;
  background: linear-gradient(to bottom, #f0f9ff, #e0f2fe);
}

/* 装饰元素 */
.page-decoration {
  @apply absolute inset-0 overflow-hidden pointer-events-none;
  z-index: 0;
}

.decoration-circle-1 {
  @apply absolute rounded-full bg-blue-500 opacity-5;
  width: 300px;
  height: 300px;
  top: -100px;
  right: -100px;
}

.decoration-circle-2 {
  @apply absolute rounded-full bg-blue-500 opacity-5;
  width: 200px;
  height: 200px;
  bottom: 20%;
  left: -100px;
}

/* 表单容器 */
.form-container {
  @apply p-5 relative z-10;
}

/* 表单头部 */
.form-header {
  @apply mb-6 text-center;
}

.form-title {
  @apply text-xl font-bold text-gray-800 mb-1;
}

.form-subtitle {
  @apply text-sm text-gray-500;
}

/* 表单分区 */
.form-section {
  @apply mb-8 bg-white rounded-xl p-5 shadow-sm;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.section-title {
  @apply text-base font-medium text-gray-800 mb-4 flex items-center;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  @apply absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-5 bg-blue-500 rounded-full;
}

/* 表单组件 */
.form-group {
  @apply mb-5;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* 输入框容器 */
.input-container {
  @apply flex items-center w-full px-4 py-2 border border-gray-200 rounded-lg bg-gray-50;
  transition: all 0.2s ease;
}

.input-container:focus-within {
  @apply border-blue-500 bg-white;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.input-icon {
  @apply mr-2;
}

.input-icon-top {
  @apply self-start mt-2;
}

.form-input {
  @apply w-full py-2 bg-transparent focus:outline-none text-gray-800;
}

.textarea-container {
  @apply items-start;
}

.form-textarea {
  @apply w-full py-2 bg-transparent focus:outline-none text-gray-800 h-24;
}

/* 头像上传器 */
.avatar-upload-container {
  @apply flex flex-col items-center;
}

.avatar-preview {
  @apply w-24 h-24 rounded-full overflow-hidden bg-blue-50 flex items-center justify-center cursor-pointer;
  border: 2px dashed rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

.avatar-preview:hover {
  @apply border-blue-500 bg-blue-50;
}

.avatar-image {
  @apply w-full h-full object-cover;
}

.avatar-placeholder {
  @apply flex flex-col items-center justify-center;
}

.upload-text {
  @apply text-xs text-blue-500 mt-1;
}

.avatar-tip {
  @apply text-xs text-gray-500 mt-2;
}

/* 提交按钮 */
.submit-button-container {
  @apply px-5 mb-8 relative z-10;
}

.submit-button {
  @apply w-full flex items-center justify-center py-3 px-4 rounded-lg bg-blue-500 text-white font-medium;
  transition: all 0.2s ease;
}

.submit-button:hover {
  @apply bg-blue-600;
}

.submit-button:active {
  @apply bg-blue-700;
}

.button-text {
  @apply ml-2;
}
</style>
