<script setup lang="ts">
import { useAppStore } from '@/stores/app'
import { useUsersStore } from '@/stores/user'

// 分页相关数据
const pageNum = ref(1)
const pageSize = ref(15)
const count = ref(0)
const isLoadingMore = ref(false)
const hasNoMoreData = ref(false)
const alertCategoryTitle = ref('创建套餐')
const alertCategory = ref(null)
const deleteConfirmPopup = ref(null)
const appConfigItems = ref([])
const appStore = useAppStore()
const userStore = useUsersStore()
const appData = ref()
const userList = ref()

const addAppConfigData = ref({
  // companyId: '',//公司id
  appId: '', // 应用Id
  type: '', // 套餐类型
  name: '', // 套餐名称
  datePrice: null, // 日套餐金额
  timePrice: null, // 次套餐金额
  dailyLimit: null, // 每日对话上限
  daysNumber: null, // 天数
  timesNumber: null, // 次数

})

function handleCategoryChange(e) {
  const index = e.detail.value
  addAppConfigData.value.appId = appData.value[index].id
}

async function getAppConfigList() {
  try {
    const result = await appStore.getCompanyAppConfigList(userList.value.current_company_id, null, pageNum.value, pageSize.value)
    console.log('套餐列表', result)
    if (result) {
      if (pageNum.value === 1) {
        appConfigItems.value = result.list
      }
      else {
        appConfigItems.value = [...appConfigItems.value, ...result.list]
      }
      count.value = result.total
    }
  }
  catch (error) {
    console.error('获取套餐列表出错:', error)
  }
  finally {
    isLoadingMore.value = false
  }
}
async function fetchUsers() {
  const result = await userStore.getCurrentUserInfo()
  if (result) {
    // 获取当前公司id

    userList.value = result
  }
}
async function getCurrentApp() {
  const result = await appStore.getCurrentCompanyApps(1, 100)

  if (result.count > 0) {
    appData.value = result.data
  }
}

function appConfig() {
  resetAppConfigData()
  alertCategory.value?.open()
}

function appConfigClose() {
  alertCategory.value?.close()
  resetAppConfigData()
}
function resetAppConfigData() {
  addAppConfigData.value = {
    appId: '', // 应用Id
    type: '', // 套餐类型
    name: '', // 套餐名称
    datePrice: null, // 套餐金额
    timePrice: null, // 次套餐金额
    dailyLimit: null, // 每日对话上限
    daysNumber: null, // 天数
    timesNumber: null, // 次数

  }
}

async function appConfigPost() {
  try {
    await appStore.addAppPackage(
      // addAppConfigData.value.companyId,
      userList.value.current_company_id,
      addAppConfigData.value.appId,
      addAppConfigData.value.type,
      addAppConfigData.value.name,
      addAppConfigData.value.timePrice === null ? 0 : Number.parseFloat(addAppConfigData.value.timePrice) * 100,
      addAppConfigData.value.datePrice === null ? 0 : Number.parseFloat(addAppConfigData.value.datePrice) * 100,
      addAppConfigData.value.dailyLimit === null ? 0 : addAppConfigData.value.dailyLimit,
      addAppConfigData.value.timesNumber === null ? 0 : addAppConfigData.value.timesNumber,
      addAppConfigData.value.daysNumber === null ? 0 : addAppConfigData.value.daysNumber,
    )
  }
  catch (err) {
    console.error('套餐操作出错:', err)
    uni.showToast({
      title: '操作失败，请稍后重试',
      icon: 'none',
      duration: 2000,
    })
  }
  appConfigClose()
  await getAppConfigList()
}

// 存储待删除的套餐 ID
const deleteAppConfigId = ref('')
// 显示删除确认弹窗
function showDeleteConfirm(id: string) {
  deleteAppConfigId.value = id
  deleteConfirmPopup.value?.open()
}
// 关闭删除确认弹窗
function closeDeleteConfirm() {
  deleteConfirmPopup.value?.close()
}
// 确认删除套餐
async function confirmDelete() {
  try {
    await appStore.deleteAppPackage(deleteAppConfigId.value)
    await getAppConfigList()
    uni.showToast({
      title: '删除成功',
      icon: 'success',
      duration: 2000,
    })
  }
  catch (error) {
    console.error('删除套餐出错:', error)
    uni.showToast({
      title: '删除失败，请稍后重试',
      icon: 'none',
      duration: 2000,
    })
  }
  closeDeleteConfirm()
}

onReachBottom(async () => {
  if (isLoadingMore.value || hasNoMoreData.value) {
    return
  }
  if (pageNum.value * pageSize.value >= count.value) {
    hasNoMoreData.value = true
    return
  }
  isLoadingMore.value = true
  pageNum.value++
  await getAppConfigList()
})

onMounted(async () => {
  await fetchUsers()
  await getCurrentApp()
  await getAppConfigList()
})

function radioChange(e: any) {
  addAppConfigData.value.type = e.detail.value
}
</script>

<template>
  <!-- Memory List -->
  <view class="shadow-xs mt-2 rounded-lg bg-white p-3">
    <view class="mb-4 flex items-center justify-between border-b border-gray-300 pb-2">
      <view class="text-lg font-medium">
        套餐列表
      </view>
      <view class="flex items-center text-blue-500" @click="appConfig">
        <uni-icons type="plusempty" size="16" color="#3B82F6" class="ml-2" />
        <view>新增套餐</view>
      </view>
    </view>

    <!-- Memory Items -->
    <view class="space-y-4">
      <view v-for="(item, index) in appConfigItems" :key="index" class="w-full border-b border-gray-100 pb-2">
        <view class="flex flex-row items-center justify-between text-xs">
          <view class="max-w-64">
            <view class="truncate font-medium text-black">
              应用名称：{{ item.app_name }}
            </view>
            <view class="truncate font-medium text-black">
              套餐名称：{{ item.name }}
            </view>
            <view class="truncate font-medium text-black">
              套餐详情：{{ item.description }}
            </view>
            <view class="truncate font-medium text-black">
              套餐类型：
              <!-- 使用三元表达式进行判断 -->
              {{ item.package_type === "PerTime" ? "次套餐" : item.package_type === "PerDay" ? "日套餐"
                : item.package_type }}
            </view>
          </view>
          <view class="flex flex-row items-baseline justify-end">
            <!-- 删除图标 -->
            <uni-icons
              type="trash" size="15" class="m-auto" color="#9CA3AF"
              @click="showDeleteConfirm(item.id)"
            />
          </view>
        </view>
      </view>
    </view>
    <!-- 加载更多提示 -->
    <view v-if="isLoadingMore" class="py-2 text-center text-gray-500">
      <uni-icons type="loading" size="20" /> 加载中...
    </view>
    <!-- 没有更多数据提示 -->
    <view v-if="hasNoMoreData" class="py-2 text-center text-gray-500">
      没有更多数据了
    </view>
  </view>

  <!-- 提示窗示例 -->
  <uni-popup ref="alertCategory" type="dialog" background-color="#ffffff" borderRadius="20px 20px 20px 20px">
    <uni-popup-dialog
      mode="input" class="!w-96" :title="alertCategoryTitle" :before-close="true"
      @close="appConfigClose" @confirm="appConfigPost"
    >
      <view class="flex flex-col items-start space-y-4 p-6">
        <view class="form-group w-full">
          <label class="mb-2 block text-sm font-medium text-gray-700">选择应用</label>
          <view class="flex items-center rounded-lg border border-gray-200 bg-gray-50 p-2">
            <uni-icons type="list" size="18" color="#3B82F6" class="mr-2" />
            <picker
              mode="selector" :range="appData" range-key="name" class="flex-1"
              @change="handleCategoryChange"
            >
              <view class="picker-content">
                {{ addAppConfigData.appId ? appData.find(c => c.id === addAppConfigData.appId)?.name
                  : '请选择应用' }}
              </view>
            </picker>
            <uni-icons type="arrowdown" size="16" color="#9CA3AF" class="ml-2" />
          </view>
        </view>
        <view class="form-group">
          <label class="mb-2 block text-sm font-medium text-gray-700">套餐类型</label>
          <view class="radio-group">
            <radio-group @change="radioChange">
              <label class="radio-option">
                <radio
                  :checked="addAppConfigData.type === 'PerDay'"
                  value="PerDay"
                  color="#3B82F6"
                />
                <text class="radio-label">日套餐</text>
              </label>
              <label class="radio-option  ml-4">
                <radio
                  :checked="addAppConfigData.type === 'PerTime'"
                  color="#3B82F6"
                  value="PerTime"
                />
                <text class="radio-label">次套餐</text>
              </label>
            </radio-group>
          </view>
        </view>
        <view class="w-full">
          <label class="mb-2 block text-sm font-medium text-gray-700">套餐名称</label>
          <input
            v-model="addAppConfigData.name"
            class="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-base transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
            placeholder="请输入套餐名称"
          >
        </view>
        <view v-if="addAppConfigData.type === 'PerTime'">
          <view class="w-full">
            <label class="mb-2 block text-sm font-medium text-gray-700">套餐金额</label>
            <input
              v-model="addAppConfigData.timePrice"
              class="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-base transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
              placeholder="请输入套餐金额"
            >
          </view>
        </view>
        <view v-if="addAppConfigData.type === 'PerDay'">
          <view class="w-full">
            <label class="mb-2 block text-sm font-medium text-gray-700">套餐金额</label>
            <input
              v-model="addAppConfigData.datePrice"
              class="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-base transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
              placeholder="请输入套餐金额"
            >
          </view>
        </view>
        <view v-if="addAppConfigData.type === 'PerDay'">
          <view class="w-full">
            <label class="mb-2 block text-sm font-medium text-gray-700">每日对话上限</label>
            <input
              v-model="addAppConfigData.dailyLimit"
              class="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-base transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
              placeholder="请输入每日对话上限"
            >
          </view>
        </view>
        <view v-if="addAppConfigData.type === 'PerTime'">
          <view class="w-full">
            <label class="mb-2 block text-sm font-medium text-gray-700">次数</label>
            <input
              v-model="addAppConfigData.timesNumber"
              class="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-base transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
              placeholder="请输入次数"
            >
          </view>
        </view>
        <view v-if="addAppConfigData.type === 'PerTime'">
          <view class="w-full">
            <label class="mb-2 block text-sm font-medium text-gray-700">天数</label>
            <input
              v-model="addAppConfigData.daysNumber"
              class="w-full rounded-lg border border-gray-200 bg-gray-50 px-4 py-3 text-base transition-all duration-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
              placeholder="请输入天数"
            >
          </view>
        </view>
      </view>
    </uni-popup-dialog>
  </uni-popup>

  <!-- 删除确认弹窗 -->
  <uni-popup ref="deleteConfirmPopup" type="dialog" background-color="#ffffff" borderRadius="20px 20px 20px 20px">
    <uni-popup-dialog
      mode="default" class="!w-96" title="确认删除" :before-close="true" @close="closeDeleteConfirm"
      @confirm="confirmDelete"
    >
      <view class="p-6 text-center">
        是否删除此套餐？
      </view>
    </uni-popup-dialog>
  </uni-popup>
</template>
