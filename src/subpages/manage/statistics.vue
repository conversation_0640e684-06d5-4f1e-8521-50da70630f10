<script lang="ts" setup>
import { useAppStore } from '@/stores/app'
import { useCompanyStore } from '@/stores/company'
import { useUsersStore } from '@/stores/user'
import { onMounted, ref } from 'vue'

const userStore = useUsersStore()
const userList = ref()
const totalData = ref()
const tokenData = ref()
const companyStore = useCompanyStore()
// 数据统计
const statisticsData = ref()

// 获取用户数据
async function fetchUsers() {
  const result = await userStore.getCurrentUserInfo()
  if (result) {
    // 获取当前公司 id
    userList.value = result
    console.log('用户数据', userList.value)
  }
}

// 获取token数据
async function fetchCompanyToken() {
  console.log('公司id', userList.value.current_company_id)
  const result = await companyStore.getCompanyToken(userList.value.current_company_id)
  console.log('获取空间的token数据', result)
  tokenData.value = result
  console.log('sdfsdfsdfsdfsd', tokenData.value)
}
// 获取统计数据
async function fetchOperatorStatistics() {
  const result = await companyStore.getOperatorStatistics()
  console.log('获取空间的统计数据', result)
  statisticsData.value = result
}
function processNumber(num: number): string | number {
  const intNum = Math.floor(num)
  if (intNum < 1000) {
    return intNum
  }
  const numInK = (intNum / 1000).toFixed(2)
  return `${numInK}k`
}

onMounted(async () => {
  // 加载用户数据
  await fetchUsers()
  // 获取token数据
  await fetchCompanyToken()
  // 获取统计数据
  await fetchOperatorStatistics()
})
</script>

<template>
  <!-- 整体容器 -->
  <view class="min-h-screen bg-gray-50 pb-4">
    <!-- TOKEN统计卡片 -->
    <view class="mx-4 mt-4 rounded-xl bg-white shadow-sm">
      <view class="border-b border-gray-100 p-4">
        <text class="text-lg font-semibold text-gray-800">Token统计</text>
      </view>
      <view class="grid grid-cols-3 gap-3 p-4">
        <!-- 累计收益 -->
        <view
          class="flex flex-col items-center rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 p-3 shadow"
        >
          <text class="mb-1 text-sm text-gray-600">总token</text>
          <text class="text-xl font-bold text-blue-600">{{ processNumber(tokenData.total_points) }}</text>
        </view>
        <!-- 生成总数 -->
        <view
          class="flex flex-col items-center rounded-lg bg-gradient-to-br from-amber-50 to-amber-100 p-3 shadow"
        >
          <text class="mb-1 text-sm text-gray-600">消耗tokn</text>
          <text class="text-xl font-bold text-amber-600">{{ processNumber(tokenData.consumed_points) }}</text>
        </view>
        <!-- 已兑换 -->
        <view
          class="flex flex-col items-center rounded-lg bg-gradient-to-br from-green-50 to-green-100 p-3 shadow"
        >
          <text class="mb-1 text-sm text-gray-600">剩余token</text>
          <text class="text-xl font-bold text-green-600">
            {{ processNumber(tokenData.available_points)
            }}
          </text>
        </view>
      </view>
    </view>

    <!-- 数据统计卡片 -->
    <view class="mx-4 mt-4 rounded-xl bg-white shadow-sm">
      <view class="border-b border-gray-100 p-4">
        <text class="text-lg font-semibold text-gray-800">数据统计</text>
      </view>
      <view class="grid grid-cols-3 gap-3 p-4">
        <!-- 第一行卡片 -->
        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">访问次数</text>
          <text class="text-xl font-bold text-blue-600">{{ statisticsData.visit_count }}</text>
        </view>

        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-amber-50 to-amber-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">访问人数</text>
          <text class="text-xl font-bold text-amber-600">{{ statisticsData.visit_users }}</text>
        </view>

        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-green-50 to-green-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">对话次数</text>
          <text class="text-xl font-bold text-green-600">{{ statisticsData.chat_count }}</text>
        </view>

        <!-- 第二行卡片 -->
        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-purple-50 to-purple-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">对话人数</text>
          <text class="text-xl font-bold text-purple-600">{{ processNumber(statisticsData.chat_users) }}</text>
        </view>

        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-pink-50 to-pink-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">人均次数</text>
          <text class="text-xl font-bold text-pink-600">{{ processNumber(statisticsData.avg_chat_per_user) }}</text>
        </view>

        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-yellow-50 to-yellow-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">转化率</text>
          <text class="text-xl font-bold text-yellow-600">{{ processNumber(statisticsData.conversion_rate) }}</text>
        </view>

        <!-- 第三行卡片 -->
        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-indigo-50 to-indigo-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">兑换次数</text>
          <text class="text-xl font-bold text-indigo-600">{{ processNumber(statisticsData.redemption_count) }}</text>
        </view>

        <view class="flex flex-col items-center rounded-lg bg-gradient-to-br from-red-50 to-red-100 p-3 shadow">
          <text class="mb-1 text-sm text-gray-600">兑换人数</text>
          <text class="text-xl font-bold text-red-600">{{ processNumber(statisticsData.redemption_users) }}</text>
        </view>
      </view>
    </view>
  </view>
</template>
