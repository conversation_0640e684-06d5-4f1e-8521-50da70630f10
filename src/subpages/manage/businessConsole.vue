<template>
    <div class="bg-white p-8 rounded shadow-md text-center">
      <h2 class="text-2xl font-bold mb-4 text-gray-800">温馨提示</h2>
      <p class="text-gray-600 mb-6">
        目前小程序端暂不支持直接进入后台管理系统。为确保功能正常使用与操作体验，您可通过以下链接在电脑端访问后台。
      </p>
      <div class="bg-gray-200 p-4 rounded mb-6">
        <span id="admin-link" class="text-blue-500 cursor-pointer select-all">
           {{ BASE_URL }}
        </span>
      </div>
      <button
        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
        @click="copyLink()"
      >
        复制后台链接
      </button>
    </div>
  </template>
  
  <script lang="ts" setup>
  const BASE_URL = import.meta.env.VITE_API_BASE
  
const copyLink = () => {
  uni.setClipboardData({
    data: BASE_URL,
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'success',
        duration: 2000
      });
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none',
        duration: 2000
      });
    }
  });
};

  </script>
  
  <style scoped>
  .select-all {
    user-select: all;
  }
  </style>  