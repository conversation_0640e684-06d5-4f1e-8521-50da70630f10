// 定义 companyApp 表的接口
export interface CompanyApp {
    // 该记录的唯一标识符，使用 UUID 保证唯一性
    id: string;
    // 关联记录创建的时间，记录空间与应用关联关系的建立时刻
    created_at: Date;
    // 当关联信息发生变化时，自动更新为当前时间
    updated_at: Date;
    // 关联的空间 ID，关联 company 表的 id 字段
    company_id?: string;
    // 关联的应用 ID，关联 app 表的 id 字段
    app_id?: string;
    // 数据插入时的用户登录态信息，以 JSON 格式存储
    raw_user_data?: Record<string, any>;
    // 商家积分的溢价率
    company_points_rate: number;
    // 商家用户积分的溢价率
    company_user_points_rate: number;
}



