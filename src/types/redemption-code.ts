import type { PackageType } from "@/modules/shared/types/company-app-user-subscription";


export interface RedemptionCode {
    id: string               // 兑换码唯一标识符
    company_id: string       // 所属公司ID
    owner_uid: string        // 创建者用户ID
    package_name: string    // 套餐名称
    package_type: PackageType    // 套餐类型（按天/按次）
    days?: number           // 套餐天数
    channel: string         // 发放渠道
    is_used: boolean        // 是否已使用
    used_by?: string        // 使用者用户ID
    used_at?: Date         // 使用时间
    created_at: Date       // 创建时间
    updated_at: Date       // 更新时间
}
