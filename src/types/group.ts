type GroupName = "superadmin"|"user"

// 定义 group 表的接口
export interface Group {
    // 组的唯一标识符，采用 UUID 类型保证全球唯一性
    id: string;
    // 记录用户组创建的具体时间
    created_at: Date;
    // 每当用户组信息发生变动，自动更新为当前时间
    updated_at: Date;
    // 组的元数据，以 JSONB 格式存储
    metadata: {
        name: GroupName;
        // 可根据实际业务需求扩展其他相关信息
    };
    // 数据插入时的用户登录态信息，以 JSON 格式存储
    raw_user_data?: Record<string, any>;
}



