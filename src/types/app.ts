
import type {
    LLMPlatformConfig
} from "@/modules/shared/types/company";

type AppType = '聊天' | '工作流' | '写作助手'


// 定义 app 表的接口
export interface App {
    // 应用的主键 ID，使用 UUID 保证唯一性
    id: string;
    // 应用创建的时间，记录应用首次在系统中注册的时间
    created_at: Date;
    // 当应用信息有更新时，自动记录当前时间
    updated_at: Date;
    // 关联的空间 ID，若应用属于特定空间，则记录该空间的 ID，为空时表示应用独立存在
    company_id?: string;
    // 应用的名称，用于标识和区分不同应用
    name: string;
    // 应用的图标链接或 Base64 编码，用于直观展示应用的特色
    icon: string;
    // 应用的简介，简要介绍应用的功能和特点
    description: string;
    // 应用的标签数组，方便对应用进行分类和检索
    tags: string[];
    // 应用是否上架，标识应用是否在系统中公开可见
    is_listed: boolean;
    // 第三方平台 LLM 配置，包含 url、platform 等信息
    third_party_llm_config: LLMPlatformConfig;
    // 应用类型，可选值为 'chat', 'workflow', 'writing_assistant'
    app_type: AppType;
    // 应用的分类 ID，
    category_id: string;
    // 数据插入时的用户登录态信息，以 JSON 格式存储
    raw_user_data?: Record<string, any>;
}



