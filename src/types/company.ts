export interface LLMPlatformConfig {
  llmPlatform: LLMPlatform;
  url: string;
}

const LLMPlatformList: LLMPlatformConfig[] = [
  {
    llmPlatform: "racio",
    // url: "https://at.racio.chat/api/v1",
    url:""
  },
  {
    llmPlatform: "coze",
    url: "https://coze.cn/api/v1",
  },
  {
    llmPlatform: "dify",
    url: "https://api.dify.ai/v1",
  },
  
  {
    llmPlatform: "custom",
    url: "",
  },
];

export function GetLLMPlatformList() {
  return LLMPlatformList;
}

// 定义 LLM 平台的可选值类型
export type LLMPlatform = "coze" | "dify" | "racio" | "custom";

// 定义 company 表的接口
export interface Company {
  // 空间的唯一标识符，采用 UUID 类型
  id: string;
  // 记录空间创建的具体时间
  created_at: Date;
  // 空间信息更新时的时间
  updated_at: Date;
  // 空间的名称，限制在 255 个字符以内
  name: string;
  // 空间的图标链接，可为空
  icon?: string;
  // 空间的详细简介
  introduction?: string;
  // 公司的名称，可为空
  company_name?: string;
  // 公司的地址信息，可为空
  company_address?: string;
  // 转换比率，用于特定业务场景下的数据转换计算
  rate?: number;
  // 智能体平台，可选值为 'coze', 'dify', 'racio', 'custom'，支持多选
  llm_platform?: LLMPlatformConfig[];
  // 空间允许容纳的最大用户数量
  max_user_count?: number;
  // 标识空间是否处于不可用状态，默认值为 false
  is_disabled: boolean;
  // 数据插入时的用户登录态信息，以 JSON 格式存储
  raw_user_data?: Record<string, any>;
}



