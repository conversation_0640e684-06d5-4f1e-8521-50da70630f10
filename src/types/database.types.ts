import type {
  SavedCompletion,
  SavedCompletionInsert,
} from '@/types/dify.type'
import type { PaymentHistory } from '@/types/payment.type'

import type { App } from './app'
import type { Company } from './company'
import type { CompanyApp } from './company-app'
import type { CompanyAppConfig } from './company-app-config'
import type { CompanyAppUserSubscription } from './company-app-user-subscription'
import type { CompanyConfig } from './company-config'
import type { CompanyUser } from './company-user'
import type { CompanyUserApp } from './company-user-app'
import type { Group } from './group'
import type { GroupUser } from './group-user'
import type { InviteLink } from './invite-link'
import type { LLMModel } from './llm-model'
import type { SessionRecord } from './session-record'
import type { SessionRecordDetail } from './session-record-detail'
import type { Tenant } from './tenant'
import type { User } from './user'
import type { UserAmountManagement } from './user-amount-management'
import type { UserPointsFlow } from './user-points-flow'
import type { UserWalletFlow } from './user-wallet-flow'

export interface Note {
  id: number
  content: string
  uid: string
  created_at: string
  updated_at: string
  user_id: string
}

export interface UserSubscription {
  id: number
  uid: string
  product_id: string
  status: string
  payment_provider: string
  start_time: string
  expire_time: string
  out_trade_no: string
  created_at: string
  updated_at: string
  last_expire_time?: string
}

export interface UserSubscriptionToken {
  id: number
  uid: string
  subscription_id: number
  total_tokens: number
  used_tokens: number
  created_at: string
  updated_at: string
}

export interface UserOnetimeToken {
  id: number
  uid: string
  payment_id: number
  total_tokens: number
  used_tokens: number
  created_at: string
  updated_at: string
}

export interface UserTokenHistory {
  id: number
  uid: string
  subscription_token_id?: number
  onetime_token_id?: number
  tokens_used: number
  model: string
  prompt_tokens: number
  completion_tokens: number
  request_id: string
  prompt: string
  completion?: string
  status: 'success' | 'error'
  error_message?: string
  created_at: string
  updated_at: string
}

export interface NuxtbaseTest {
  id: number
  title: string
  name: string
  content: string
  created_at: string
  updated_at: string
  room_id: number
  transfer_time: Date
  price: number
  exchange_number: number
  start_time: Date
  end_time: Date
  status: number
  total_token: number
  remaining_token: number
  consumed_token: number
  type: number
  icon: string
  label: string
}

type ModelInsert<T> = Omit<T, 'id' | 'created_at' | 'updated_at' | 'user_id'>
type ModelUpdate<T> = Partial<
  Omit<T, 'id' | 'created_at' | 'updated_at' | 'user_id'>
>

export interface Database {
  public: {
    Tables: {
      nuxtbase_demo_notes: {
        Row: Note
        Insert: Omit<Note, 'id' | 'created_at' | 'updated_at' | 'user_id'>
        Update: Partial<
          Omit<Note, 'id' | 'created_at' | 'updated_at' | 'user_id'>
        >
      }
      nuxtbase_payment_history: {
        Row: PaymentHistory
        Insert: Omit<PaymentHistory, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<
          Omit<PaymentHistory, 'id' | 'created_at' | 'updated_at'>
        >
      }
      nuxtbase_user_subscription: {
        Row: UserSubscription
        Insert: Omit<UserSubscription, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<
          Omit<UserSubscription, 'id' | 'created_at' | 'updated_at'>
        >
      }
      nuxtbase_user_subscription_token: {
        Row: UserSubscriptionToken
        Insert: Omit<UserSubscriptionToken, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<
          Omit<UserSubscriptionToken, 'id' | 'created_at' | 'updated_at'>
        >
      }
      nuxtbase_user_onetime_token: {
        Row: UserOnetimeToken
        Insert: Omit<UserOnetimeToken, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<
          Omit<UserOnetimeToken, 'id' | 'created_at' | 'updated_at'>
        >
      }
      nuxtbase_user_token_history: {
        Row: UserTokenHistory
        Insert: Omit<UserTokenHistory, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<
          Omit<UserTokenHistory, 'id' | 'created_at' | 'updated_at'>
        >
      }
      nuxtbase_test: {
        Row: NuxtbaseTest
        Insert: Omit<NuxtbaseTest, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<NuxtbaseTest, 'id' | 'created_at' | 'updated_at'>>
      }
      dify_text_completion_saved: {
        Row: SavedCompletion
        Insert: SavedCompletionInsert
        Update: Partial<SavedCompletionInsert>
      }
      App: {
        Row: App
        Insert: ModelInsert<App>
        Update: ModelUpdate<App>
      }
      Company: {
        Row: Company
        Insert: ModelInsert<Company>
        Update: ModelUpdate<Company>
      }
      CompanyUser: {
        Row: CompanyUser
        Insert: ModelInsert<CompanyUser>
        Update: ModelUpdate<CompanyUser>
      }
      InviteLink: {
        Row: InviteLink
        Insert: ModelInsert<InviteLink>
        Update: ModelUpdate<InviteLink>
      }
      Tenant: {
        Row: Tenant
        Insert: ModelInsert<Tenant>
        Update: ModelUpdate<Tenant>
      }
      CompanyApp: {
        Row: CompanyApp
        Insert: ModelInsert<CompanyApp>
        Update: ModelUpdate<CompanyApp>
      }
      CompanyUserApp: {
        Row: CompanyUserApp
        Insert: ModelInsert<CompanyUserApp>
        Update: ModelUpdate<CompanyUserApp>
      }
      Group: {
        Row: Group
        Insert: ModelInsert<Group>
        Update: ModelUpdate<Group>
      }
      GroupUser: {
        Row: GroupUser
        Insert: ModelInsert<GroupUser>
        Update: ModelUpdate<GroupUser>
      }
      User: {
        Row: User
        Insert: ModelInsert<User>
        Update: ModelUpdate<User>
      }
      CompanyAppConfig: {
        Row: CompanyAppConfig
        Insert: ModelInsert<CompanyAppConfig>
        Update: ModelUpdate<CompanyAppConfig>
      }
      CompanyAppUserSubscription: {
        Row: CompanyAppUserSubscription
        Insert: ModelInsert<CompanyAppUserSubscription>
        Update: ModelUpdate<CompanyAppUserSubscription>
      }
      CompanyConfig: {
        Row: CompanyConfig
        Insert: ModelInsert<CompanyConfig>
        Update: ModelUpdate<CompanyConfig>
      }
      LLMModel: {
        Row: LLMModel
        Insert: ModelInsert<LLMModel>
        Update: ModelUpdate<LLMModel>
      }
      SessionRecord: {
        Row: SessionRecord
        Insert: ModelInsert<SessionRecord>
        Update: ModelUpdate<SessionRecord>
      }
      SessionRecordDetail: {
        Row: SessionRecordDetail
        Insert: ModelInsert<SessionRecordDetail>
        Update: ModelUpdate<SessionRecordDetail>
      }
      UserAmountManagement: {
        Row: UserAmountManagement
        Insert: ModelInsert<UserAmountManagement>
        Update: ModelUpdate<UserAmountManagement>
      }
      UserPointsFlow: {
        Row: UserPointsFlow
        Insert: ModelInsert<UserPointsFlow>
        Update: ModelUpdate<UserPointsFlow>
      }
      UserWalletFlow: {
        Row: UserWalletFlow
        Insert: ModelInsert<UserWalletFlow>
        Update: ModelUpdate<UserWalletFlow>
      }
    }
  }
}
