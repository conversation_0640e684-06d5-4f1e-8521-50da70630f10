// 定义消息类型的联合类型
type MessageType = 'question' | 'answer';

export interface SessionRecordDetail {
    // 记录的唯一标识 ID
    id: string;
    // 创建时间
    created_at: Date;
    // 更新时间
    updated_at: Date;
    // 会话记录的唯一标识 ID
    session_record_id: string;
    // 消息的 ID
    llm_model_message_id?: string;
    // 平台的成本，单位：分
    platform_cost: number;
    // 空间的成本，单位：分
    company_cost: number;
    // 用户的成本，单位：分
    user_cost: number;
    // 消息类型，区分用户提问和模型回答
    message_type: MessageType;
}



