// 定义收支类型的联合类型
type PointsIncomeExpenseType = 'Income' | 'Expense';
// 定义流水类型的联合类型
type PointsFlowType = 'DailyExpense' | 'PerTimeExpense' | 'PerQuantityExpense' |'UserPerQuantityExpense' | 'UnsubscribeIncome' | 'Recharge';

export interface UserPointsFlow {
    // 表唯一标识 ID
    id: string;
    // 创建时间
    created_at: Date;
    // 更新时间
    updated_at: Date;
    // 用户唯一标识 ID
    uid: string;
    // 空间唯一标识 ID
    company_id?: string;
    // 应用唯一标识 ID
    app_id?: string;
    // 收支类型
    income_expense_type: PointsIncomeExpenseType;
    // 积分
    points: number;
    // 备注
    remark?: string;
    // 流水类型
    flow_type: PointsFlowType;
    // 关联钱包流水唯一标识 ID
    associated_wallet_flow_id?: string;
    // 关联会话唯一标识 ID
    associated_session_id?: string;
    // 关联会话详情唯一标识 ID
    associated_session_detail_id?: string;
}



