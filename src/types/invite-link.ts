import type {
  LLMPlatformConfig,
} from '@/types/company'

// 定义邀请类型的可选值类型
export type InviteType = 'invite_to_create_company' | 'invite_to_manage_company' | 'invite_to_join_company'

// 定义 inviteLink 表的接口
export interface InviteLink {
  // 邀请链接的唯一标识符，使用 UUID 保证链接的唯一性和安全性
  id: string
  // 邀请链接创建的时间，记录链接生成的具体时刻
  created_at: Date
  // 当邀请链接的相关信息发生更改时，自动更新为当前时间
  updated_at: Date
  // 邀请类型，明确链接的用途
  invite_type: InviteType
  // 邀请创建空间时有效，智能体平台，可多选
  llm_platform?: LLMPlatformConfig[]
  // 邀请链接已使用的次数，初始值为 0
  usaged: number
  // 标识邀请链接是否不可用，默认值为 false
  is_disabled: boolean
  // 邀请链接的备注信息，可为用户提供关于链接的额外说明
  remark?: string
  // 邀请链接的限制使用次数，默认值为 0，表示无限制
  limit?: number
  // 关联的空间 ID，若邀请链接与特定空间相关，则记录该空间的 ID
  company_id?: string
  // 数据插入时的用户登录态信息，以 JSON 格式存储
  raw_user_data?: Record<string, any>
}
