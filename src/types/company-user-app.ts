// 定义 companyUserApp 表的接口
export interface CompanyUserApp {
    // 该记录的唯一标识符，使用 UUID 确保唯一性
    id: string;
    // 关联记录创建的时间，记录用户收藏应用这一操作的时间
    created_at: Date;
    // 当用户收藏应用的相关信息发生变化时，自动更新为当前时间
    updated_at: Date;
    // 关联的空间 ID，关联 company 表的 id 字段
    company_id?: string;
    // 用户的唯一标识符，关联用户表中的对应字段
    uid: string;
    // 关联的应用 ID，关联 app 表的 id 字段
    app_id?: string;
    // 数据插入时的用户登录态信息，以 JSON 格式存储
    raw_user_data?: Record<string, any>;
}



