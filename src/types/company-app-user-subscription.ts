// 定义订阅类型的联合类型
type SubscriptionType = 'Gift' | 'SelfPaid';
// 定义套餐类型的联合类型
type PackageType = 'PerTime' | 'PerDay' | 'DynamicPremium';

// 定义空间应用用户订阅管理表的接口
export interface CompanyAppUserSubscription {
    // 记录的唯一标识 ID，使用 UUID 保证唯一性
    id: string;
    // 记录创建的时间，记录该条信息首次在系统中创建的时间
    created_at: Date;
    // 当记录信息有更新时，自动记录当前时间
    updated_at: Date;
    // 用户唯一标识 ID，使用 UUID 保证唯一性
    uid: string;
    // 空间唯一标识 ID，使用 UUID 保证唯一性
    company_id: string;
    // 应用唯一标识 ID，使用 UUID 保证唯一性
    app_id: string;
    // 生效时间
    effective_time: Date;
    // 过期时间
    expiry_time: Date;
    // 订阅类型，如 Gift（赠予）、SelfPaid（自费）
    subscription_type: SubscriptionType;
    // 套餐类型，可选值：PerTime（按次）、PerDay（包日）
    package_type: PackageType;
    
    // 每日的积分数，仅包日套餐有效
    points_per_day?: number;
    // 每日的最大使用次数，仅包日套餐有效
    max_usage_per_day?: number;
    // 最后使用时间，仅包日套餐有效
    last_used_time?: Date;
    // 最后一天使用次数，仅包日套餐有效
    last_day_used_count?: number;
    
    // 每次的积分数，仅按次套餐有效
    points_per_time?: number;
    // 订阅数，仅按次套餐有效
    subscription_count: number;
    // 使用数，仅按次套餐有效
    used_count: number;
}


