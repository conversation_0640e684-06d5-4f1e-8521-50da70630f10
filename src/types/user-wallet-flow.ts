/**
 * 定义收支类型的联合类型
 * 此联合类型用于表示某笔交易是收入还是支出
 * - 'Income': 表示收入，意味着资金流入账户
 * - 'Expense': 表示支出，意味着资金从账户流出
 */
type IncomeExpenseType = 'Income' | 'Expense';

/**
 * 定义流水类型的联合类型
 * 此联合类型用于描述钱包资金流动的具体场景
 * - 'Recharge': 表示充值操作，即用户向钱包中注入资金
 * - 'PersonalConsumption': 表示个人消费，即用户使用钱包资金进行个人的消费行为
 * - 'SpaceUserConsumption': 表示空间用户消费，可能是在特定空间内的用户进行的消费
 * - 'CommissionIncome': 表示佣金收入，即用户通过某种方式获得的佣金报酬
 * - 'Withdrawal': 表示提现操作，即用户从钱包中提取资金到其他账户
 */
type WalletFlowType = 'Recharge' | 'PersonalConsumption' | 'SpaceUserConsumption' | 'CommissionIncome' | 'Withdrawal';

/**
 * 定义账单类型的联合类型
 * 此联合类型用于描述账单的不同状态
 * - 'Recorded': 表示已记录，即账单已经被记录在系统中
 * - 'Recording': 表示正在记录，即账单正在被系统记录的过程中
 */
type BillType =  'Recorded' | 'Recording';

export interface UserWalletFlow {
    // 表唯一标识 ID
    id: string;
    // 创建时间
    created_at: Date;
    // 更新时间
    updated_at: Date;
    // 交易时间
    transaction_time: Date;
    // 用户唯一标识 ID
    uid: string;
    // 收支类型
    income_expense_type: IncomeExpenseType;
    // 金额，单位：分
    amount: number;
    // 备注
    remark?: string;
    // 流水类型
    flow_type: WalletFlowType;
    // 账单类型
    bill_type: BillType;
    // 本次提现所扣除的手续费金额，单位：分
    withdrawal_fee?: number;
    // 关联套餐id
    company_app_user_subscription_id?: string;
}



