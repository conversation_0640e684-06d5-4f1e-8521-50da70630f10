// 定义用户金额积分管理表的接口
export interface UserAmountManagement {
    // 记录的唯一标识 ID，使用 UUID 保证唯一性，不可为空
    id: string;
    // 记录创建的时间，不可为空，默认值为当前时间
    created_at: Date;
    // 当记录信息有更新时，自动记录当前时间，不可为空，默认值为当前时间
    updated_at: Date;
    // 用户的唯一标识 ID，不可为空
    uid: string;
    // 用户的总金额，单位：分，不可为空，默认值为 0
    total_amount: number;
    // 用户的消费金额，单位：分，不可为空，默认值为 0
    consumed_amount: number;
    // 用户的未入账金额，单位：分，不可为空，默认值为 0
    unrecorded_amount: number;
    // 用户的总积分，不可为空，默认值为 0
    total_points: number;
    // 用户的消费积分，不可为空，默认值为 0
    consumed_points: number;
    // 积分余额提示阈值，当积分低于该值时可提醒用户，不可为空，默认值为 0
    points_threshold: number;
}


