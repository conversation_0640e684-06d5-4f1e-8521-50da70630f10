export interface SessionRecord {
    // 记录的唯一标识 ID
    id: string;
    // 创建时间
    created_at: Date;
    // 更新时间
    updated_at: Date;
    // 用户的唯一标识 ID
    uid: string;
    // 空间的唯一标识 ID
    company_id: string;
    // 应用的唯一标识 ID
    app_id: string;
    // llm 模型的唯一标识 ID
    llm_model_id?: string;
    // llm 会话的 ID
    llm_model_session_id?: string;
    // 会话开始时间
    start_time: Date;
    // 会话结束时间，若会话未结束则为空
    end_time?: Date;
    // 会话总费用，精确到小数点后两位，单位：分
    total_cost: number;
    // 会话消耗的总积分数
    total_points_cost: number;
}



