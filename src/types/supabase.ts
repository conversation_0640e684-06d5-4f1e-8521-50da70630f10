export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  dbdev: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      install: {
        Args: {
          package_name: string
          base_url?: string
          api_key?: string
        }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  pgbouncer: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_auth: {
        Args: {
          p_usename: string
        }
        Returns: {
          username: string
          password: string
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  pgtle: {
    Tables: {
      feature_info: {
        Row: {
          feature: Database["pgtle"]["Enums"]["pg_tle_features"]
          obj_identity: string
          proname: string
          schema_name: string
        }
        Insert: {
          feature: Database["pgtle"]["Enums"]["pg_tle_features"]
          obj_identity: string
          proname: string
          schema_name: string
        }
        Update: {
          feature?: Database["pgtle"]["Enums"]["pg_tle_features"]
          obj_identity?: string
          proname?: string
          schema_name?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      available_extension_versions: {
        Args: Record<PropertyKey, never>
        Returns: Record<string, unknown>[]
      }
      available_extensions: {
        Args: Record<PropertyKey, never>
        Returns: Record<string, unknown>[]
      }
      create_base_type: {
        Args: {
          typenamespace: unknown
          typename: unknown
          infunc: unknown
          outfunc: unknown
          internallength: number
        }
        Returns: undefined
      }
      create_base_type_if_not_exists: {
        Args: {
          typenamespace: unknown
          typename: unknown
          infunc: unknown
          outfunc: unknown
          internallength: number
        }
        Returns: boolean
      }
      create_operator_func: {
        Args: {
          typenamespace: unknown
          typename: unknown
          opfunc: unknown
        }
        Returns: undefined
      }
      create_operator_func_if_not_exists: {
        Args: {
          typenamespace: unknown
          typename: unknown
          opfunc: unknown
        }
        Returns: boolean
      }
      create_shell_type: {
        Args: {
          typenamespace: unknown
          typename: unknown
        }
        Returns: undefined
      }
      create_shell_type_if_not_exists: {
        Args: {
          typenamespace: unknown
          typename: unknown
        }
        Returns: boolean
      }
      extension_update_paths: {
        Args: {
          name: unknown
        }
        Returns: Record<string, unknown>[]
      }
      install_extension: {
        Args: {
          name: string
          version: string
          description: string
          ext: string
          requires?: string[]
        }
        Returns: boolean
      }
      install_extension_version_sql: {
        Args: {
          name: string
          version: string
          ext: string
        }
        Returns: boolean
      }
      install_update_path: {
        Args: {
          name: string
          fromvers: string
          tovers: string
          ext: string
        }
        Returns: boolean
      }
      "pointsource-supabase_rbac--0.0.1--0.0.2.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "pointsource-supabase_rbac--0.0.1--0.0.3.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "pointsource-supabase_rbac--0.0.1.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "pointsource-supabase_rbac--0.0.2--0.0.3.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "pointsource-supabase_rbac--0.0.2.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "pointsource-supabase_rbac--0.0.3--0.0.4.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "pointsource-supabase_rbac--1.0.0.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "pointsource-supabase_rbac--2.0.0.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "pointsource-supabase_rbac--2.0.1.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "pointsource-supabase_rbac--2.0.2.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "pointsource-supabase_rbac--3.0.0.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "pointsource-supabase_rbac--4.0.0.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "pointsource-supabase_rbac.control": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      register_feature: {
        Args: {
          proc: unknown
          feature: Database["pgtle"]["Enums"]["pg_tle_features"]
        }
        Returns: undefined
      }
      register_feature_if_not_exists: {
        Args: {
          proc: unknown
          feature: Database["pgtle"]["Enums"]["pg_tle_features"]
        }
        Returns: boolean
      }
      set_default_version: {
        Args: {
          name: string
          version: string
        }
        Returns: boolean
      }
      "supabase-dbdev--0.0.2.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "supabase-dbdev--0.0.3.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "supabase-dbdev--0.0.4.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "supabase-dbdev--0.0.5.sql": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      "supabase-dbdev.control": {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      uninstall_extension:
        | {
            Args: {
              extname: string
            }
            Returns: boolean
          }
        | {
            Args: {
              extname: string
              version: string
            }
            Returns: boolean
          }
      uninstall_extension_if_exists: {
        Args: {
          extname: string
        }
        Returns: boolean
      }
      uninstall_update_path: {
        Args: {
          extname: string
          fromvers: string
          tovers: string
        }
        Returns: boolean
      }
      uninstall_update_path_if_exists: {
        Args: {
          extname: string
          fromvers: string
          tovers: string
        }
        Returns: boolean
      }
      unregister_feature: {
        Args: {
          proc: unknown
          feature: Database["pgtle"]["Enums"]["pg_tle_features"]
        }
        Returns: undefined
      }
      unregister_feature_if_exists: {
        Args: {
          proc: unknown
          feature: Database["pgtle"]["Enums"]["pg_tle_features"]
        }
        Returns: boolean
      }
    }
    Enums: {
      password_types:
        | "PASSWORD_TYPE_PLAINTEXT"
        | "PASSWORD_TYPE_MD5"
        | "PASSWORD_TYPE_SCRAM_SHA_256"
      pg_tle_features: "passcheck" | "clientauth"
    }
    CompositeTypes: {
      clientauth_port_subset: {
        noblock: boolean
        remote_host: string
        remote_hostname: string
        remote_hostname_resolv: number
        remote_hostname_errcode: number
        database_name: string
        user_name: string
      }
    }
  }
  preference: {
    Tables: {
      content_detail: {
        Row: {
          content_id: string
          createdAt: string
          favorite: boolean | null
          interval: string | null
          layout: Json[] | null
          period_end_date: string | null
          period_end_time_period: string | null
          period_start_date: string | null
          period_start_time_period: string | null
          schema_version: string | null
          sql: string | null
          updatedAt: string
        }
        Insert: {
          content_id?: string
          createdAt: string
          favorite?: boolean | null
          interval?: string | null
          layout?: Json[] | null
          period_end_date?: string | null
          period_end_time_period?: string | null
          period_start_date?: string | null
          period_start_time_period?: string | null
          schema_version?: string | null
          sql?: string | null
          updatedAt: string
        }
        Update: {
          content_id?: string
          createdAt?: string
          favorite?: boolean | null
          interval?: string | null
          layout?: Json[] | null
          period_end_date?: string | null
          period_end_time_period?: string | null
          period_start_date?: string | null
          period_start_time_period?: string | null
          schema_version?: string | null
          sql?: string | null
          updatedAt?: string
        }
        Relationships: []
      }
      user_content: {
        Row: {
          content_id: string
          createdAt: string
          description: string | null
          id: string
          inserted_at: string | null
          name: string
          owner_id: string | null
          type: Database["preference"]["Enums"]["enum_user_content_type"]
          updated_at: string | null
          updatedAt: string
          visibility:
            | Database["preference"]["Enums"]["enum_user_content_visibility"]
            | null
        }
        Insert: {
          content_id: string
          createdAt: string
          description?: string | null
          id?: string
          inserted_at?: string | null
          name: string
          owner_id?: string | null
          type: Database["preference"]["Enums"]["enum_user_content_type"]
          updated_at?: string | null
          updatedAt: string
          visibility?:
            | Database["preference"]["Enums"]["enum_user_content_visibility"]
            | null
        }
        Update: {
          content_id?: string
          createdAt?: string
          description?: string | null
          id?: string
          inserted_at?: string | null
          name?: string
          owner_id?: string | null
          type?: Database["preference"]["Enums"]["enum_user_content_type"]
          updated_at?: string | null
          updatedAt?: string
          visibility?:
            | Database["preference"]["Enums"]["enum_user_content_visibility"]
            | null
        }
        Relationships: [
          {
            foreignKeyName: "user_content_content_id_fkey"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey1"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey10"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey11"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey12"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey13"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey14"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey15"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey16"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey17"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey18"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey2"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey3"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey4"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey5"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey6"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey7"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey8"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          },
          {
            foreignKeyName: "user_content_content_id_fkey9"
            columns: ["content_id"]
            referencedRelation: "content_detail"
            referencedColumns: ["content_id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      enum_user_content_type: "sql" | "report" | "log_sql"
      enum_user_content_visibility: "user" | "project" | "org" | "public"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      app_categories: {
        Row: {
          created_at: string
          description: string
          id: string
          raw_user_data: Json | null
          title: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string
          id?: string
          raw_user_data?: Json | null
          title?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string
          id?: string
          raw_user_data?: Json | null
          title?: string
          updated_at?: string
        }
        Relationships: []
      }
      apps: {
        Row: {
          apikey: string
          app_type: string
          category_id: string
          company_id: string | null
          created_at: string
          description: string
          icon: string
          id: string
          is_listed: boolean
          model_type: string
          name: string
          raw_user_data: Json | null
          tags: string[]
          updated_at: string
        }
        Insert: {
          apikey?: string
          app_type: string
          category_id: string
          company_id?: string | null
          created_at?: string
          description: string
          icon: string
          id?: string
          is_listed?: boolean
          model_type?: string
          name: string
          raw_user_data?: Json | null
          tags: string[]
          updated_at?: string
        }
        Update: {
          apikey?: string
          app_type?: string
          category_id?: string
          company_id?: string | null
          created_at?: string
          description?: string
          icon?: string
          id?: string
          is_listed?: boolean
          model_type?: string
          name?: string
          raw_user_data?: Json | null
          tags?: string[]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "apps_category_id_fkey"
            columns: ["category_id"]
            referencedRelation: "app_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "apps_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          }
        ]
      }
      companies: {
        Row: {
          company_address: string
          company_name: string
          company_points_rate: number
          created_at: string
          icon: string
          id: string
          introduction: string
          is_disabled: boolean
          llm_platform: Json | null
          max_user_count: number
          name: string
          rate: number
          raw_user_data: Json | null
          updated_at: string
        }
        Insert: {
          company_address?: string
          company_name?: string
          company_points_rate?: number
          created_at?: string
          icon?: string
          id?: string
          introduction?: string
          is_disabled?: boolean
          llm_platform?: Json | null
          max_user_count?: number
          name?: string
          rate?: number
          raw_user_data?: Json | null
          updated_at?: string
        }
        Update: {
          company_address?: string
          company_name?: string
          company_points_rate?: number
          created_at?: string
          icon?: string
          id?: string
          introduction?: string
          is_disabled?: boolean
          llm_platform?: Json | null
          max_user_count?: number
          name?: string
          rate?: number
          raw_user_data?: Json | null
          updated_at?: string
        }
        Relationships: []
      }
      company_app: {
        Row: {
          app_id: string
          company_id: string
          company_user_points_rate: number
          created_at: string
          id: string
          is_free: boolean
          new_user_try_time: number
          opening_statement: string
          picture_remark: string
          raw_user_data: Json | null
          updated_at: string
        }
        Insert: {
          app_id: string
          company_id: string
          company_user_points_rate?: number
          created_at?: string
          id?: string
          is_free?: boolean
          new_user_try_time?: number
          opening_statement?: string
          picture_remark?: string
          raw_user_data?: Json | null
          updated_at?: string
        }
        Update: {
          app_id?: string
          company_id?: string
          company_user_points_rate?: number
          created_at?: string
          id?: string
          is_free?: boolean
          new_user_try_time?: number
          opening_statement?: string
          picture_remark?: string
          raw_user_data?: Json | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "company_app_app_id_fkey"
            columns: ["app_id"]
            referencedRelation: "apps"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_app_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          }
        ]
      }
      company_app_config: {
        Row: {
          app_id: string
          company_id: string
          created_at: string
          days: number
          id: string
          max_usage_per_day: number
          name: string
          package_type: string
          points_per_day: number
          points_per_time: number
          raw_user_data: Json | null
          times: number
          updated_at: string
        }
        Insert: {
          app_id: string
          company_id: string
          created_at?: string
          days?: number
          id?: string
          max_usage_per_day?: number
          name?: string
          package_type?: string
          points_per_day?: number
          points_per_time?: number
          raw_user_data?: Json | null
          times?: number
          updated_at?: string
        }
        Update: {
          app_id?: string
          company_id?: string
          created_at?: string
          days?: number
          id?: string
          max_usage_per_day?: number
          name?: string
          package_type?: string
          points_per_day?: number
          points_per_time?: number
          raw_user_data?: Json | null
          times?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "company_app_config_app_id_fkey"
            columns: ["app_id"]
            referencedRelation: "apps"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_app_config_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          }
        ]
      }
      company_app_user_subscription: {
        Row: {
          app_id: string
          company_id: string
          created_at: string
          effective_time: string
          expiry_time: string
          id: string
          is_subscription_valid: boolean
          last_day_used_count: number
          last_used_time: string | null
          max_usage_per_day: number
          package_type: string
          points_per_day: number
          points_per_time: number
          raw_user_data: Json
          subscription_count: number
          subscription_type: string
          uid: string
          updated_at: string
          used_count: number
        }
        Insert: {
          app_id: string
          company_id: string
          created_at?: string
          effective_time: string
          expiry_time: string
          id?: string
          is_subscription_valid?: boolean
          last_day_used_count?: number
          last_used_time?: string | null
          max_usage_per_day?: number
          package_type?: string
          points_per_day?: number
          points_per_time?: number
          raw_user_data: Json
          subscription_count?: number
          subscription_type?: string
          uid: string
          updated_at?: string
          used_count?: number
        }
        Update: {
          app_id?: string
          company_id?: string
          created_at?: string
          effective_time?: string
          expiry_time?: string
          id?: string
          is_subscription_valid?: boolean
          last_day_used_count?: number
          last_used_time?: string | null
          max_usage_per_day?: number
          package_type?: string
          points_per_day?: number
          points_per_time?: number
          raw_user_data?: Json
          subscription_count?: number
          subscription_type?: string
          uid?: string
          updated_at?: string
          used_count?: number
        }
        Relationships: [
          {
            foreignKeyName: "company_app_user_subscription_app_id_fkey"
            columns: ["app_id"]
            referencedRelation: "apps"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_app_user_subscription_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_app_user_subscription_uid_fkey"
            columns: ["uid"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      company_config: {
        Row: {
          company_id: string
          company_point_rate: number
          created_at: string
          id: string
          raw_user_data: Json | null
          updated_at: string
          withdrawal_fee_rate: number
        }
        Insert: {
          company_id: string
          company_point_rate?: number
          created_at?: string
          id?: string
          raw_user_data?: Json | null
          updated_at?: string
          withdrawal_fee_rate?: number
        }
        Update: {
          company_id?: string
          company_point_rate?: number
          created_at?: string
          id?: string
          raw_user_data?: Json | null
          updated_at?: string
          withdrawal_fee_rate?: number
        }
        Relationships: [
          {
            foreignKeyName: "company_config_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          }
        ]
      }
      company_user: {
        Row: {
          company_id: string
          created_at: string
          id: string
          is_admin: boolean
          is_owner: boolean
          last_use_time: string | null
          latest_use_app_id: string | null
          raw_user_data: Json | null
          uid: string
          updated_at: string
        }
        Insert: {
          company_id: string
          created_at?: string
          id?: string
          is_admin?: boolean
          is_owner?: boolean
          last_use_time?: string | null
          latest_use_app_id?: string | null
          raw_user_data?: Json | null
          uid: string
          updated_at?: string
        }
        Update: {
          company_id?: string
          created_at?: string
          id?: string
          is_admin?: boolean
          is_owner?: boolean
          last_use_time?: string | null
          latest_use_app_id?: string | null
          raw_user_data?: Json | null
          uid?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "company_user_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_user_latest_use_app_id_fkey"
            columns: ["latest_use_app_id"]
            referencedRelation: "apps"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_user_uid_fkey"
            columns: ["uid"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      company_user_app: {
        Row: {
          app_id: string
          company_id: string
          created_at: string
          id: string
          raw_user_data: Json | null
          uid: string
          updated_at: string
        }
        Insert: {
          app_id: string
          company_id: string
          created_at?: string
          id?: string
          raw_user_data?: Json | null
          uid: string
          updated_at?: string
        }
        Update: {
          app_id?: string
          company_id?: string
          created_at?: string
          id?: string
          raw_user_data?: Json | null
          uid?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "company_user_app_app_id_fkey"
            columns: ["app_id"]
            referencedRelation: "apps"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_user_app_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_user_app_uid_fkey"
            columns: ["uid"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      dify_text_completion_saved: {
        Row: {
          answer: string
          app_id: string | null
          created_at: string
          id: string
          query: string
          user_id: string
        }
        Insert: {
          answer: string
          app_id?: string | null
          created_at?: string
          id: string
          query: string
          user_id: string
        }
        Update: {
          answer?: string
          app_id?: string | null
          created_at?: string
          id?: string
          query?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "dify_text_completion_saved_app_id_fkey"
            columns: ["app_id"]
            referencedRelation: "apps"
            referencedColumns: ["id"]
          }
        ]
      }
      group_users: {
        Row: {
          created_at: string
          group_id: string
          id: string
          raw_user_data: Json | null
          uid: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          group_id: string
          id?: string
          raw_user_data?: Json | null
          uid: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          group_id?: string
          id?: string
          raw_user_data?: Json | null
          uid?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "group_users_group_id_fkey"
            columns: ["group_id"]
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_users_uid_fkey"
            columns: ["uid"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      groups: {
        Row: {
          created_at: string
          id: string
          metadata: Json
          name: string
          raw_user_data: Json | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          metadata: Json
          name?: string
          raw_user_data?: Json | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          metadata?: Json
          name?: string
          raw_user_data?: Json | null
          updated_at?: string
        }
        Relationships: []
      }
      invite_link: {
        Row: {
          company_id: string | null
          created_at: string
          creator_uid: string | null
          id: string
          invite_type: string
          is_disabled: boolean
          limit: number
          llm_platform: Json | null
          raw_user_data: Json | null
          remark: string
          updated_at: string
          usaged: number
        }
        Insert: {
          company_id?: string | null
          created_at?: string
          creator_uid?: string | null
          id?: string
          invite_type?: string
          is_disabled?: boolean
          limit?: number
          llm_platform?: Json | null
          raw_user_data?: Json | null
          remark?: string
          updated_at?: string
          usaged?: number
        }
        Update: {
          company_id?: string | null
          created_at?: string
          creator_uid?: string | null
          id?: string
          invite_type?: string
          is_disabled?: boolean
          limit?: number
          llm_platform?: Json | null
          raw_user_data?: Json | null
          remark?: string
          updated_at?: string
          usaged?: number
        }
        Relationships: [
          {
            foreignKeyName: "invite_link_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invite_link_creator_uid_fkey"
            columns: ["creator_uid"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      llm_model: {
        Row: {
          created_at: string
          id: string
          model_name: string
          model_provider: string
          premium_rate: number
          raw_user_data: Json | null
          unique_code: string
          updated_at: string
        }
        Insert: {
          created_at: string
          id?: string
          model_name?: string
          model_provider?: string
          premium_rate?: number
          raw_user_data?: Json | null
          unique_code?: string
          updated_at: string
        }
        Update: {
          created_at?: string
          id?: string
          model_name?: string
          model_provider?: string
          premium_rate?: number
          raw_user_data?: Json | null
          unique_code?: string
          updated_at?: string
        }
        Relationships: []
      }
      nuxtbase_demo_notes: {
        Row: {
          content: string
          created_at: string
          id: number
          updated_at: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: never
          updated_at?: string
          user_id?: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: never
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      nuxtbase_payment_history: {
        Row: {
          amount: number
          created_at: string
          currency: string
          id: number
          meta: Json | null
          out_trade_no: string
          payment_mode: string
          payment_provider: string
          product_id: string
          product_name: string
          status: string
          uid: string
          updated_at: string
        }
        Insert: {
          amount: number
          created_at?: string
          currency?: string
          id?: number
          meta?: Json | null
          out_trade_no: string
          payment_mode: string
          payment_provider: string
          product_id: string
          product_name: string
          status: string
          uid: string
          updated_at?: string
        }
        Update: {
          amount?: number
          created_at?: string
          currency?: string
          id?: number
          meta?: Json | null
          out_trade_no?: string
          payment_mode?: string
          payment_provider?: string
          product_id?: string
          product_name?: string
          status?: string
          uid?: string
          updated_at?: string
        }
        Relationships: []
      }
      nuxtbase_test: {
        Row: {
          consumed_token: number | null
          content: string | null
          created_at: string
          end_time: string | null
          exchange_number: number | null
          icon: string | null
          id: number
          label: string | null
          name: string | null
          price: number | null
          remaining_token: number | null
          role: number | null
          room_id: number | null
          start_time: string | null
          status: number | null
          title: string | null
          total_token: number | null
          transfer_time: string | null
          type: number | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          consumed_token?: number | null
          content?: string | null
          created_at?: string
          end_time?: string | null
          exchange_number?: number | null
          icon?: string | null
          id?: number
          label?: string | null
          name?: string | null
          price?: number | null
          remaining_token?: number | null
          role?: number | null
          room_id?: number | null
          start_time?: string | null
          status?: number | null
          title?: string | null
          total_token?: number | null
          transfer_time?: string | null
          type?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          consumed_token?: number | null
          content?: string | null
          created_at?: string
          end_time?: string | null
          exchange_number?: number | null
          icon?: string | null
          id?: number
          label?: string | null
          name?: string | null
          price?: number | null
          remaining_token?: number | null
          role?: number | null
          room_id?: number | null
          start_time?: string | null
          status?: number | null
          title?: string | null
          total_token?: number | null
          transfer_time?: string | null
          type?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      nuxtbase_user_onetime_token: {
        Row: {
          created_at: string
          id: number
          payment_id: number
          total_tokens: number
          uid: string
          updated_at: string
          used_tokens: number
        }
        Insert: {
          created_at?: string
          id?: number
          payment_id: number
          total_tokens: number
          uid: string
          updated_at?: string
          used_tokens?: number
        }
        Update: {
          created_at?: string
          id?: number
          payment_id?: number
          total_tokens?: number
          uid?: string
          updated_at?: string
          used_tokens?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_payment"
            columns: ["payment_id"]
            referencedRelation: "nuxtbase_payment_history"
            referencedColumns: ["id"]
          }
        ]
      }
      nuxtbase_user_subscription: {
        Row: {
          created_at: string
          expire_time: string
          id: number
          out_trade_no: string
          payment_provider: string
          product_id: string
          start_time: string
          status: string
          uid: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          expire_time: string
          id?: number
          out_trade_no: string
          payment_provider: string
          product_id: string
          start_time: string
          status: string
          uid: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          expire_time?: string
          id?: number
          out_trade_no?: string
          payment_provider?: string
          product_id?: string
          start_time?: string
          status?: string
          uid?: string
          updated_at?: string
        }
        Relationships: []
      }
      nuxtbase_user_subscription_token: {
        Row: {
          created_at: string
          id: number
          subscription_id: number
          total_tokens: number
          uid: string
          updated_at: string
          used_tokens: number
        }
        Insert: {
          created_at?: string
          id?: number
          subscription_id: number
          total_tokens?: number
          uid: string
          updated_at?: string
          used_tokens?: number
        }
        Update: {
          created_at?: string
          id?: number
          subscription_id?: number
          total_tokens?: number
          uid?: string
          updated_at?: string
          used_tokens?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_subscription"
            columns: ["subscription_id"]
            referencedRelation: "nuxtbase_user_subscription"
            referencedColumns: ["id"]
          }
        ]
      }
      nuxtbase_user_token_history: {
        Row: {
          created_at: string
          id: number
          model: string
          onetime_token_id: number | null
          subscription_token_id: number | null
          tokens_used: number
          uid: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: number
          model: string
          onetime_token_id?: number | null
          subscription_token_id?: number | null
          tokens_used: number
          uid: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: number
          model?: string
          onetime_token_id?: number | null
          subscription_token_id?: number | null
          tokens_used?: number
          uid?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "nuxtbase_user_token_history_onetime_token_id_fkey"
            columns: ["onetime_token_id"]
            referencedRelation: "nuxtbase_user_onetime_token"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "nuxtbase_user_token_history_subscription_token_id_fkey"
            columns: ["subscription_token_id"]
            referencedRelation: "nuxtbase_user_subscription_token"
            referencedColumns: ["id"]
          }
        ]
      }
      redemption_code: {
        Row: {
          app_id: string
          channel: string
          code: string
          company_app_config_id: string
          company_id: string
          created_at: string
          days: number
          id: string
          is_deleted: boolean
          is_used: boolean
          owner_uid: string
          raw_user_data: Json | null
          updated_at: string
          used_at: string | null
          used_by: string | null
        }
        Insert: {
          app_id: string
          channel?: string
          code?: string
          company_app_config_id: string
          company_id: string
          created_at?: string
          days?: number
          id?: string
          is_deleted?: boolean
          is_used?: boolean
          owner_uid: string
          raw_user_data?: Json | null
          updated_at?: string
          used_at?: string | null
          used_by?: string | null
        }
        Update: {
          app_id?: string
          channel?: string
          code?: string
          company_app_config_id?: string
          company_id?: string
          created_at?: string
          days?: number
          id?: string
          is_deleted?: boolean
          is_used?: boolean
          owner_uid?: string
          raw_user_data?: Json | null
          updated_at?: string
          used_at?: string | null
          used_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "redemption_code_app_id_fkey"
            columns: ["app_id"]
            referencedRelation: "apps"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "redemption_code_company_app_config_id_fkey"
            columns: ["company_app_config_id"]
            referencedRelation: "company_app_config"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "redemption_code_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "redemption_code_owner_uid_fkey"
            columns: ["owner_uid"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "redemption_code_used_by_fkey"
            columns: ["used_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      session_record_detail: {
        Row: {
          app_id: string
          company_cost: number
          company_id: string
          company_points_rate: number
          company_user_points_rate: number
          completion_tokens: number
          created_at: string
          id: string
          llm_model_id: string
          llm_premium_rate: number
          platform_cost: number
          prompt_tokens: number
          raw_user_data: Json | null
          uid: string
          updated_at: string
          usage: Json | null
          user_cost: number
        }
        Insert: {
          app_id: string
          company_cost?: number
          company_id: string
          company_points_rate?: number
          company_user_points_rate?: number
          completion_tokens?: number
          created_at?: string
          id?: string
          llm_model_id: string
          llm_premium_rate?: number
          platform_cost?: number
          prompt_tokens?: number
          raw_user_data?: Json | null
          uid: string
          updated_at?: string
          usage?: Json | null
          user_cost?: number
        }
        Update: {
          app_id?: string
          company_cost?: number
          company_id?: string
          company_points_rate?: number
          company_user_points_rate?: number
          completion_tokens?: number
          created_at?: string
          id?: string
          llm_model_id?: string
          llm_premium_rate?: number
          platform_cost?: number
          prompt_tokens?: number
          raw_user_data?: Json | null
          uid?: string
          updated_at?: string
          usage?: Json | null
          user_cost?: number
        }
        Relationships: [
          {
            foreignKeyName: "session_record_detail_app_id_fkey"
            columns: ["app_id"]
            referencedRelation: "apps"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "session_record_detail_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "session_record_detail_llm_model_id_fkey"
            columns: ["llm_model_id"]
            referencedRelation: "llm_model"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "session_record_detail_uid_fkey"
            columns: ["uid"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      tenants: {
        Row: {
          company_id: string
          created_at: string
          effective_date: string
          expiration_date: string
          id: string
          raw_user_data: Json | null
          updated_at: string
        }
        Insert: {
          company_id: string
          created_at?: string
          effective_date: string
          expiration_date: string
          id?: string
          raw_user_data?: Json | null
          updated_at?: string
        }
        Update: {
          company_id?: string
          created_at?: string
          effective_date?: string
          expiration_date?: string
          id?: string
          raw_user_data?: Json | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "tenants_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          }
        ]
      }
      user: {
        Row: {
          avatar: string
          created_at: string
          current_company_id: string | null
          email: string
          feature: string
          id: string
          industry: string
          job: string
          latest_login_time: string | null
          nickname: string
          phone: string
          raw_app_meta_data: Json | null
          service: string
          uid: string
          updated_at: string
          wechat_union_id: string
        }
        Insert: {
          avatar?: string
          created_at?: string
          current_company_id?: string | null
          email?: string
          feature?: string
          id?: string
          industry?: string
          job?: string
          latest_login_time?: string | null
          nickname?: string
          phone?: string
          raw_app_meta_data?: Json | null
          service?: string
          uid: string
          updated_at?: string
          wechat_union_id?: string
        }
        Update: {
          avatar?: string
          created_at?: string
          current_company_id?: string | null
          email?: string
          feature?: string
          id?: string
          industry?: string
          job?: string
          latest_login_time?: string | null
          nickname?: string
          phone?: string
          raw_app_meta_data?: Json | null
          service?: string
          uid?: string
          updated_at?: string
          wechat_union_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_current_company_id_fkey"
            columns: ["current_company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_uid_fkey"
            columns: ["uid"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_amount_management: {
        Row: {
          consumed_amount: number
          consumed_points: number
          created_at: string
          id: string
          points_threshold: number
          raw_user_data: Json | null
          total_amount: number
          total_points: number
          uid: string
          unrecorded_amount: number
          updated_at: string
        }
        Insert: {
          consumed_amount?: number
          consumed_points?: number
          created_at?: string
          id?: string
          points_threshold?: number
          raw_user_data?: Json | null
          total_amount?: number
          total_points?: number
          uid: string
          unrecorded_amount?: number
          updated_at?: string
        }
        Update: {
          consumed_amount?: number
          consumed_points?: number
          created_at?: string
          id?: string
          points_threshold?: number
          raw_user_data?: Json | null
          total_amount?: number
          total_points?: number
          uid?: string
          unrecorded_amount?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_points_flow_uid_fkey"
            columns: ["uid"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_memory: {
        Row: {
          created_at: string
          feature: string
          id: string
          industry: string
          job: string
          service: string
          uid: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          feature?: string
          id?: string
          industry?: string
          job?: string
          service?: string
          uid: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          feature?: string
          id?: string
          industry?: string
          job?: string
          service?: string
          uid?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_memory_uid_fkey"
            columns: ["uid"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_points_flow: {
        Row: {
          app_id: string | null
          associated_session_detail_id: string | null
          associated_wallet_flow_id: string | null
          company_app_user_subscription_id: string | null
          company_id: string | null
          created_at: string
          flow_type: string
          id: string
          income_expense_type: string
          order_no: string
          points: number
          raw_user_data: Json | null
          remark: string
          uid: string | null
          updated_at: string
        }
        Insert: {
          app_id?: string | null
          associated_session_detail_id?: string | null
          associated_wallet_flow_id?: string | null
          company_app_user_subscription_id?: string | null
          company_id?: string | null
          created_at?: string
          flow_type?: string
          id?: string
          income_expense_type?: string
          order_no?: string
          points?: number
          raw_user_data?: Json | null
          remark?: string
          uid?: string | null
          updated_at?: string
        }
        Update: {
          app_id?: string | null
          associated_session_detail_id?: string | null
          associated_wallet_flow_id?: string | null
          company_app_user_subscription_id?: string | null
          company_id?: string | null
          created_at?: string
          flow_type?: string
          id?: string
          income_expense_type?: string
          order_no?: string
          points?: number
          raw_user_data?: Json | null
          remark?: string
          uid?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_points_flow_app_id_fkey"
            columns: ["app_id"]
            referencedRelation: "apps"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_points_flow_associated_session_detail_id_fkey"
            columns: ["associated_session_detail_id"]
            referencedRelation: "session_record_detail"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_points_flow_associated_wallet_flow_id_fkey"
            columns: ["associated_wallet_flow_id"]
            referencedRelation: "user_wallet_flow"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_points_flow_company_app_user_subscription_id_fkey"
            columns: ["company_app_user_subscription_id"]
            referencedRelation: "company_app_user_subscription"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_points_flow_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_points_flow_uid_fkey"
            columns: ["uid"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      user_wallet_flow: {
        Row: {
          amount: number
          bill_type: string
          company_app_user_subscription_id: string | null
          company_id: string | null
          created_at: string
          flow_type: string
          id: string
          income_expense_type: string
          order_no: string
          raw_user_data: Json | null
          remark: string
          transaction_time: string | null
          uid: string | null
          updated_at: string
          withdrawal_fee: number
        }
        Insert: {
          amount?: number
          bill_type?: string
          company_app_user_subscription_id?: string | null
          company_id?: string | null
          created_at?: string
          flow_type?: string
          id?: string
          income_expense_type?: string
          order_no?: string
          raw_user_data?: Json | null
          remark?: string
          transaction_time?: string | null
          uid?: string | null
          updated_at?: string
          withdrawal_fee?: number
        }
        Update: {
          amount?: number
          bill_type?: string
          company_app_user_subscription_id?: string | null
          company_id?: string | null
          created_at?: string
          flow_type?: string
          id?: string
          income_expense_type?: string
          order_no?: string
          raw_user_data?: Json | null
          remark?: string
          transaction_time?: string | null
          uid?: string | null
          updated_at?: string
          withdrawal_fee?: number
        }
        Relationships: [
          {
            foreignKeyName: "user_wallet_flow_company_app_user_subscription_id_fkey"
            columns: ["company_app_user_subscription_id"]
            referencedRelation: "company_app_user_subscription"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_wallet_flow_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_wallet_flow_uid_fkey"
            columns: ["uid"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      app_add_company_app_config: {
        Args: {
          p_company_id: string
          p_app_id: string
          p_package_type: string
          p_name: string
          p_points_per_time?: number
          p_points_per_day?: number
          p_max_usage_per_day?: number
          p_times?: number
          p_days?: number
        }
        Returns: {
          id: string
        }[]
      }
      app_category_create: {
        Args: {
          p_title: string
          p_description: string
        }
        Returns: {
          id: string
        }[]
      }
      app_category_delete: {
        Args: {
          p_category_id: string
        }
        Returns: undefined
      }
      app_category_update: {
        Args: {
          p_category_id: string
          p_title: string
          p_description: string
        }
        Returns: undefined
      }
      app_create: {
        Args: {
          p_company_id: string
          p_name: string
          p_icon: string
          p_description: string
          p_tags: string[]
          p_app_type: string
          p_apikey?: string
          p_model_type?: string
          p_category_id?: string
          p_company_user_points_rate?: number
          p_opening_statement?: string
        }
        Returns: {
          id: string
        }[]
      }
      app_delete: {
        Args: {
          p_id: string
        }
        Returns: undefined
      }
      app_delete_company_app_config: {
        Args: {
          p_id: string
        }
        Returns: undefined
      }
      app_is_available: {
        Args: {
          p_id: string
        }
        Returns: boolean
      }
      app_toggle_listing: {
        Args: {
          p_id: string
          p_is_listed: boolean
        }
        Returns: undefined
      }
      app_update: {
        Args: {
          p_id: string
          p_name: string
          p_icon: string
          p_description: string
          p_tags: string[]
          p_app_type: string
          p_apikey?: string
          p_model_type?: string
          p_category_id?: string
          p_company_user_points_rate?: number
          p_opening_statement?: string
        }
        Returns: undefined
      }
      calculate_chat_consumption: {
        Args: {
          p_uid: string
          p_llm_model_session_id: string
          p_llm_model_message_id: string
          p_message: string
          p_consumed_points: number
          p_llm_model_id: string
          p_company_id: string
          p_app_id: string
        }
        Returns: {
          platform_cost: number
          company_cost: number
          user_cost: number
        }[]
      }
      chat_calculate_consumption: {
        Args: {
          p_uid: string
          p_consumed_points: number
          p_llm_model_id: string
          p_company_id: string
          p_app_id: string
          p_prompt_tokens: number
          p_completion_tokens: number
          p_usage: Json
        }
        Returns: {
          platform_cost: number
          company_cost: number
          user_cost: number
        }[]
      }
      chat_session_record_create: {
        Args: {
          p_uid: string
          p_llm_model_conversation_id: string
          p_company_id: string
          p_app_id: string
          p_llm_model_id: string
        }
        Returns: {
          session_record_id: string
        }[]
      }
      check_user_has_sufficient_points: {
        Args: {
          p_uid: string
        }
        Returns: boolean
      }
      check_user_subscription: {
        Args: {
          p_uid: string
          p_company_id: string
          p_app_id: string
        }
        Returns: {
          id: string
          uid: string
          company_id: string
          app_id: string
          package_type: string
          used_count: number
          subscription_count: number
          expiry_time: string
          is_subscription_valid: boolean
        }[]
      }
      check_user_sufficient_points: {
        Args: {
          p_uid: string
          p_company_id: string
          p_app_id: string
        }
        Returns: boolean
      }
      company_app_update: {
        Args: {
          p_company_id: string
          p_app_id: string
          p_is_free?: boolean
          p_picture_remark?: string
          p_new_user_try_time?: number
        }
        Returns: undefined
      }
      company_check_disabled: {
        Args: {
          p_company_id: string
        }
        Returns: boolean
      }
      company_create_or_update_user: {
        Args: {
          p_uid: string
          p_nickname?: string
          p_avatar?: string
          p_wechat_union_id?: string
          p_phone?: string
          p_email?: string
        }
        Returns: {
          user_id: string
        }[]
      }
      company_remove_company_admin_status: {
        Args: {
          p_uid: string
          p_company_id: string
        }
        Returns: undefined
      }
      company_remove_user_from_company: {
        Args: {
          p_uid: string
          p_company_id: string
        }
        Returns: undefined
      }
      company_set_company_user_as_admin: {
        Args: {
          p_uid: string
          p_company_id: string
        }
        Returns: undefined
      }
      company_set_disabled: {
        Args: {
          p_company_id: string
          p_is_disabled: boolean
        }
        Returns: undefined
      }
      company_set_tenant: {
        Args: {
          p_company_id: string
          p_effective_date: string
          p_expiration_date: string
        }
        Returns: {
          id: string
        }[]
      }
      company_switch_user_current_company: {
        Args: {
          p_uid: string
          p_current_company_id: string
        }
        Returns: undefined
      }
      company_update: {
        Args: {
          p_company_id: string
          p_name?: string
          p_company_name?: string
          p_company_address?: string
          p_introduction?: string
          p_icon?: string
        }
        Returns: undefined
      }
      company_update_last_login_time: {
        Args: {
          p_uid: string
        }
        Returns: undefined
      }
      company_update_user_info: {
        Args: {
          p_uid: string
          p_nickname: string
          p_avatar: string
          p_wechat_union_id: string
          p_phone: string
          p_email: string
        }
        Returns: undefined
      }
      delete_category_and_subcategories: {
        Args: {
          category_id: string
        }
        Returns: undefined
      }
      generate_order_no: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_available_subscription: {
        Args: {
          p_uid: string
          p_company_id: string
          p_app_id: string
        }
        Returns: {
          subscription: unknown
        }[]
      }
      get_company_expense_flow_list: {
        Args: {
          p_page?: number
          p_size?: number
          p_company_id?: string
          p_start_time?: string
          p_end_time?: string
        }
        Returns: Json
      }
      get_company_points_expense_flow: {
        Args: {
          p_page: number
          p_size: number
          p_start_time?: string
          p_end_time?: string
          p_company_id?: string
          p_app_id?: string
          p_user_id?: string
        }
        Returns: Json
      }
      get_platform_expense_flow: {
        Args: {
          p_page: number
          p_size: number
          p_start_time?: string
          p_end_time?: string
          p_company_id?: string
          p_app_id?: string
          p_user_id?: string
          p_llm_model_id?: string
        }
        Returns: Json
      }
      get_platform_income_flow: {
        Args: {
          p_page?: number
          p_size?: number
          p_start_time?: string
          p_end_time?: string
        }
        Returns: Json
      }
      get_subcategory_count_for_category: {
        Args: {
          category_id: string
        }
        Returns: number
      }
      get_user_remaining_points: {
        Args: {
          p_uid: string
        }
        Returns: number
      }
      get_user_role_info: {
        Args: {
          current_user_uid: string
        }
        Returns: {
          user_role_name: string
          current_company_id: string
          current_company_role_name: string
        }[]
      }
      invite_link_create: {
        Args: {
          p_invite_type: string
          p_llm_platform?: Json
          p_limit?: number
          p_company_id?: string
          p_creator_uid?: string
        }
        Returns: {
          id: string
        }[]
      }
      invite_link_create_company: {
        Args: {
          p_invite_link_id: string
          p_name: string
          p_uid: string
        }
        Returns: {
          company_id: string
          company_user_id: string
        }[]
      }
      invite_link_join: {
        Args: {
          p_invite_link_id: string
          p_uid: string
        }
        Returns: {
          company_id: string
          company_user_id: string
        }[]
      }
      invite_link_join_manage: {
        Args: {
          p_invite_link_id: string
          p_uid: string
        }
        Returns: {
          company_id: string
          company_user_id: string
        }[]
      }
      invite_link_toggle_status: {
        Args: {
          p_id: string
          p_is_disabled: boolean
        }
        Returns: undefined
      }
      llm_model_create: {
        Args: {
          p_model_name: string
          p_model_provider: string
          p_unique_code: string
          p_premium_rate: number
          p_raw_user_data?: Json
        }
        Returns: {
          id: string
        }[]
      }
      llm_model_delete: {
        Args: {
          p_id: string
        }
        Returns: undefined
      }
      llm_model_update: {
        Args: {
          p_id: string
          p_model_name: string
          p_model_provider: string
          p_unique_code: string
          p_premium_rate: number
          p_raw_user_data?: Json
        }
        Returns: undefined
      }
      points_create_redemption_codes: {
        Args: {
          p_uid: string
          p_company_app_config_id: string
          p_days: number
          p_channel: string
          p_count: number
        }
        Returns: string[]
      }
      points_delete_redemption_code: {
        Args: {
          p_code_id: string
        }
        Returns: undefined
      }
      points_exchange_amount_for_points: {
        Args: {
          p_uid: string
          p_amount: number
        }
        Returns: {
          user_wallet_flow_id: string
          user_amount_id: string
          user_points_flow_id: string
        }[]
      }
      points_get_exchange_records: {
        Args: {
          p_page?: number
          p_size?: number
          p_company_id?: string
        }
        Returns: Json
      }
      points_gift_app_points: {
        Args: {
          p_uid: string
          p_app_id: string
          p_package_type: string
          p_quantity: number
        }
        Returns: {
          company_id: string
          subscription_id: string
        }[]
      }
      points_recharge_and_exchange: {
        Args: {
          p_company_id: string
          p_transaction_time: string
          p_amount: number
          p_points: number
          p_remark: string
        }
        Returns: {
          v_owner_uid: string
          user_wallet_flow_id: string
          user_amount_id: string
          user_points_flow_id: string
        }[]
      }
      points_recharge_to_wallet: {
        Args: {
          p_uid: string
          p_amount: number
        }
        Returns: {
          user_wallet_flow_id: string
          user_amount_id: string
        }[]
      }
      points_subscribe_app_package_per_day: {
        Args: {
          p_uid: string
          p_company_app_config_id: string
          p_quantity: number
        }
        Returns: {
          user_amount_id: string
          owner_amount_id: string
          subscription_id: string
          user_points_flow_id: string
          owner_wallet_flow_id: string
        }[]
      }
      points_subscribe_app_package_per_time: {
        Args: {
          p_uid: string
          p_company_app_config_id: string
        }
        Returns: {
          user_amount_id: string
          owner_amount_id: string
          subscription_id: string
          user_points_flow_id: string
          owner_wallet_flow_id: string
        }[]
      }
      points_use_redemption_code: {
        Args: {
          p_uid: string
          p_code: string
        }
        Returns: string
      }
      points_withdraw: {
        Args: {
          p_uid: string
          p_amount: number
        }
        Returns: {
          user_wallet_flow_id: string
          user_amount_id: string
        }[]
      }
      process_expired_subscription: {
        Args: {
          p_subscription_record: unknown
        }
        Returns: undefined
      }
      query: {
        Args: {
          sql: string
        }
        Returns: Json[]
      }
      update_expired_subscriptions: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      user_update_job_industry: {
        Args: {
          p_uid: string
          p_job: string
          p_industry: string
        }
        Returns: undefined
      }
      user_update_latest_use_app_id: {
        Args: {
          p_company_id: string
          p_uid: string
          p_latest_use_app_id: string
        }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  sqlj: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null
          avif_autodetection: boolean | null
          created_at: string | null
          file_size_limit: number | null
          id: string
          name: string
          owner: string | null
          owner_id: string | null
          public: boolean | null
          updated_at: string | null
        }
        Insert: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id: string
          name: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id?: string
          name?: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      migrations: {
        Row: {
          executed_at: string | null
          hash: string
          id: number
          name: string
        }
        Insert: {
          executed_at?: string | null
          hash: string
          id: number
          name: string
        }
        Update: {
          executed_at?: string | null
          hash?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      objects: {
        Row: {
          bucket_id: string | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          metadata: Json | null
          name: string | null
          owner: string | null
          owner_id: string | null
          path_tokens: string[] | null
          updated_at: string | null
          version: string | null
        }
        Insert: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          version?: string | null
        }
        Update: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "objects_bucketId_fkey"
            columns: ["bucket_id"]
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      can_insert_object: {
        Args: {
          bucketid: string
          name: string
          owner: string
          metadata: Json
        }
        Returns: undefined
      }
      extension: {
        Args: {
          name: string
        }
        Returns: string
      }
      filename: {
        Args: {
          name: string
        }
        Returns: string
      }
      foldername: {
        Args: {
          name: string
        }
        Returns: unknown
      }
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>
        Returns: {
          size: number
          bucket_id: string
        }[]
      }
      search: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  tiger: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  topology: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (Database["public"]["Tables"] & Database["public"]["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (Database["public"]["Tables"] &
      Database["public"]["Views"])
  ? (Database["public"]["Tables"] &
      Database["public"]["Views"])[PublicTableNameOrOptions] extends {
      Row: infer R
    }
    ? R
    : never
  : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof Database["public"]["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof Database["public"]["Tables"]
  ? Database["public"]["Tables"][PublicTableNameOrOptions] extends {
      Insert: infer I
    }
    ? I
    : never
  : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof Database["public"]["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof Database["public"]["Tables"]
  ? Database["public"]["Tables"][PublicTableNameOrOptions] extends {
      Update: infer U
    }
    ? U
    : never
  : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof Database["public"]["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof Database["public"]["Enums"]
  ? Database["public"]["Enums"][PublicEnumNameOrOptions]
  : never
