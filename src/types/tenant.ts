// 定义 tenant 表的接口
export interface Tenant {
    // 租期记录的唯一标识符，使用 UUID 确保唯一性
    id: string;
    // 租期记录创建的时间，记录该租期信息首次录入系统的时刻
    created_at: Date;
    // 当租期信息发生变化时，自动更新为当前时间
    updated_at: Date;
    // 关联的空间 ID，关联 company 表的 id 字段
    company_id?: string;
    // 租期的生效日期，精确到具体时间
    effective_date: Date;
    // 租期的到期日期，精确到具体时间
    expiration_date: Date;
    // 数据插入时的用户登录态信息，以 JSON 格式存储
    raw_user_data?: Record<string, any>;
}



