// 定义 user 表的接口
export interface User {
    // 该记录的唯一标识符，使用 UUID 类型保证唯一性
    id: string;
    // 用户创建的时间，记录用户首次在系统中注册的时刻
    created_at: Date;
    // 每当用户信息有更新，自动记录当前时间
    updated_at: Date;
    // 用户的唯一标识符，可关联其他与用户相关的表
    uid: string;
    // 用户当前使用的空间 ID，关联 company 表的 id 字段
    current_company_id?: string;
    // 最后登录时间
    lastest_login_time: Date;
    // 昵称
    nickname: string;
    // 头像
    avatar: string;
    // 微信唯一ID
    wechat_union_id: string;
    // 电话号码
    phone: string
    // 邮箱
    email: string
    // 数据插入时的用户登录态信息，以 JSON 格式存储
    raw_user_data?: Record<string, any>;
    //是否超级管理员
    is_super_admin: boolean;
    //是否空间管理员
    is_admin: boolean;
    //是否空间所有者
    is_owner: boolean;
}



