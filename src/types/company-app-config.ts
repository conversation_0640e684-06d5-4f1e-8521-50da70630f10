// 定义套餐类型的联合类型
type PackageType = 'PerTime' | 'PerDay' | 'DynamicPremium';

// 定义公司应用配置表的接口
export interface CompanyAppConfig {
    // 记录的唯一标识 ID，使用 UUID 保证唯一性
    id: string;
    // 记录创建的时间，记录该条信息首次在系统中创建的时间
    created_at: Date;
    // 当记录信息有更新时，自动记录当前时间
    updated_at: Date;
    // 公司（空间）的唯一标识 ID，使用 UUID 保证唯一性
    company_id: string;
    // 应用的唯一标识 ID，使用 UUID 保证唯一性
    app_id: string;
    // 套餐名称
    name: string;
    // 套餐类型，可选值：PerTime（按次）、PerDay（包日）
    package_type: PackageType;
    // 每次的积分数，仅按次套餐有效
    points_per_time?: number;
    // 每日的积分数，仅包日套餐有效
    points_per_day?: number;
    // 每日的最大使用次数，仅包日套餐有效
    max_usage_per_day?: number;
    // 次数，仅包日套餐有效
    times?: number;
    // 有效天数
    days?: number; 
}


