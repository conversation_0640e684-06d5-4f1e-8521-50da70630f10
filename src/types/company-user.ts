type CompanyUserRole = "companyowner"| "companyadmin"|"user"

// 定义 companyUser 表的接口
export interface CompanyUser {
    // 该记录的唯一标识符，使用 UUID 确保唯一性
    id: string;
    // 记录用户关联空间这一操作的创建时间
    created_at: Date;
    // 当用户在空间中的相关信息发生变化时，自动更新为当前时间
    updated_at: Date;
    // 用户的唯一标识符，关联用户表中的对应字段
    uid: string;
    // 所属空间的 ID，关联 company 表的 id 字段
    company_id?: string;
    // 用户最后使用该空间的时间，便于系统了解用户的活跃情况
    last_use_time?: Date;
    // 标识用户是否为该空间的拥有者，方便系统进行权限管理
    is_owner?: boolean;
    // 标识用户是否为该空间的管理员，用于区分用户在空间中的不同管理权限
    is_admin?: boolean;
    // 数据插入时的用户登录态信息，以 JSON 格式存储
    raw_user_data?: Record<string, any>;
}



