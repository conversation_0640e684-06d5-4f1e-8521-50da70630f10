export const DB_TABLES = {
  PAYMENT_HISTORY: 'nuxtbase_payment_history',
  USER_SUBSCRIPTION: 'nuxtbase_user_subscription',
  NOTE: 'nuxtbase_demo_notes',
  TEST: 'nuxtbase_test',
  USER_SUBSCRIPTION_TOKEN: 'nuxtbase_user_subscription_token',
  USER_ONETIME_TOKEN: 'nuxtbase_user_onetime_token',
  USER_TOKEN_HISTORY: 'nuxtbase_user_token_history',
  DIFY_TEXT_COMPLETION_SAVED: 'dify_text_completion_saved',

  USER: 'user',
  APP: 'apps',
  APP_CATEGORY: 'app_categories',
  USER_EX: 'user_ex',
  COMPANY: 'companies',
  USER_APP_FOLLOW: 'user_app_follows',
  GROUP: 'groups',
  GROUP_USER: 'group_users',
  GROUP_INVITES: 'group_invites',
  TENANT: 'tenants',
  COMPANY_USER: 'company_user',
  INVITE_LINK: 'invite_link',
  COMPANY_APP: 'company_app',
  COMPANY_BILLING_RECORDS: 'company_billing_records',
  COMPANY_POINTS_RECORDS: 'company_points_records',
  COMPANY_USER_APP: 'company_user_app',
  LLM_MODEL: 'llm_model',
  COMPANY_CONFIG: 'company_config',
  COMPANY_APP_CONFIG: 'company_app_config',
  COMPANY_APP_USER_SUBSCRIPTION: 'company_app_user_subscription',
  USER_AMOUNT_MANAGEMENT: 'user_amount_management',
  SESSION_RECORD: 'session_record',
  SESSION_RECORD_DETAIL: 'session_record_detail',
  USER_WALLET_FLOW: 'user_wallet_flow',
  USER_POINTS_FLOW: 'user_points_flow',
}
