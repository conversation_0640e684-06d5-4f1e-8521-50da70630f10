{
  "easycom": {
    "autoscan": true,
    "custom": {
    // uni-ui 规则如下配置
      "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    }
  },
  "pages": [ // pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "backgroundColor": "#fff"
      }
    },
    {
      "path": "pages/my/index",
      "style": {
        "navigationBarTitleText": "我的",
        "backgroundColor": "#F8F9FA",
        "backgroundColorBottom": "#F8F9FA"
      }
    },
    {
      "path": "pages/auth/login",
      "style": {
        "disableScroll": true,
        "navigationBarTitleText": "登录"
      }
    },
    {
      "path": "pages/my/info",
      "style": {

        "navigationBarTitleText": "个人信息"
      }
    },
    {
      "path": "pages/agent/chat",
      "style": {
        "disableScroll": true,
        "navigationBarTitleText": "智能体对话",
        "backgroundColor": "#F8F9FA",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"

      }
    },
    {
      "path": "pages/agent/create",
      "style": {
        "navigationBarTitleText": "智能体"
      }
    },
    {
      "path": "pages/agent/completion",
      "style": {
        "disableScroll": true,
        "navigationBarTitleText": "智能体对话",
        "backgroundColor": "#F8F9FA",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    }, {
      "path": "pages/agent/workflow",
      "style": {
        "disableScroll": true,
        "navigationBarTitleText": "智能体对话",
        "backgroundColor": "#F8F9FA",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },

    {
      "path": "pages/workspace/join",
      "style": {
        "navigationBarTitleText": "加入工作空间"
      }
    },

    {
      "path": "pages/workspace/invite",
      "style": {
        "navigationBarTitleText": "邀请加入工作空间"
      }
    },
    {
      "path": "pages/auth/bindPhone",
      "style": {
        "disableScroll": true,
        "navigationBarTitleText": "补充信息"
      }
    },
    {
      "path": "pages/pricing/index",
      "style": {
        "navigationBarTitleText": "智能体价格表",
        "navigationBarTextStyle": "white",
        "navigationBarBackgroundColor": "#00c853"
      }
    },
    {
      "path": "pages/test/markdown-test",
      "style": {
        "navigationBarTitleText": "Markdown Copy Test"
      }
    }
  ],
  "subPackages": [
    {
      "root": "subpages",
      "pages": [
        {
          "path": "feedback/feedback",
          "style": {
            "navigationBarTitleText": "提个建议"
          }
        },
        {
          "path": "feedback/help",
          "style": {
            "navigationBarTitleText": "使用帮助"
          }
        },
        {
          "path": "feedback/about",
          "style": {
            "navigationBarTitleText": "关于我们"
          }
        },
        {
          "path": "manage/statistics",
          "style": {
            "navigationBarTitleText": "数据统计"
          }
        },
        {
          "path": "manage/redeemCode",
          "style": {
            "navigationBarTitleText": "兑换码管理"
          }
        },
        {
          "path": "manage/businessConsole",
          "style": {
            "navigationBarTitleText": "进入商家后台"
          }
        },
        {
          "path": "recharge/index",
          "style": {
            "disableScroll": true,
            "navigationBarTitleText": "充值"
          }
        },
        {
          "path": "workspace/create",
          "style": {
            "navigationBarTitleText": "创建工作空间"
          }
        },
        {
          "path": "workspace/category",
          "style": {
            "navigationBarTitleText": "分类管理"
          }
        },
        {
          "path": "workspace/appConfig",
          "style": {
            "navigationBarTitleText": "套餐管理"
          }
        }
      ]
    },
    {
      "root": "subdeclaration",
      "pages": [
        {
          "path": "declaration/agreement",
          "style": {
            "disableScroll": true,
            "navigationBarTitleText": "用户协议"
          }
        },
        {
          "path": "declaration/privacyPolicy",
          "style": {
            "disableScroll": true,
            "navigationBarTitleText": "隐私政策"
          }
        }
      ]
    }
  ],
  "preloadRule": {
    "pages/my/index": {
      "network": "all",
      "packages": [
        "subpages"
      ]
    },
    "pages/auth/login": {
      "network": "all",
      "packages": [
        "subdeclaration"
      ]
    }
  },
  "plugins": {
    "WechatSI": {
      "version": "0.3.6",
      "provider": "wx069ba97219f66d99"
    }
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "weapp-tailwindcss",
    "navigationBarBackgroundColor": "#F8F9FA",
    "backgroundColor": "#F8F9FA",
    "backgroundColorBottom": "#F8F9FA"
  },
  "tabBar": {
    "color": "#bfbfbf",
    "selectedColor": "#1B1BB3",
    "backgroundColor": "#fff",
    "borderStyle": "black",
    "fontSize": "16px",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "/static/tabs/index.png",
        "selectedIconPath": "/static/tabs/index-active.png"
      },
      {
        "pagePath": "pages/my/index",
        "text": "我的",
        "iconPath": "/static/tabs/accounts.png",
        "selectedIconPath": "/static/tabs/accounts-active.png"
      }
    ]
  }
}
