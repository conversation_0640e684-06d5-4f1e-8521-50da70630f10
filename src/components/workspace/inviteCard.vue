<script setup lang="ts">
import { useAppStore } from '@/stores/app'
import { useInviteLinkStore } from '@/stores/workspace'

import { ref } from 'vue'

const props = defineProps({
  item: {
    type: Object as () => InviteLink,
    required: true,
  },
})
const emits = defineEmits(['toggle-status', 'copy-invite-link', 'show-qrcode', 'set-share-prams'])

const appStore = useAppStore()
const inviteLinkStore = useInviteLinkStore()

// 定义 props 的类型
interface InviteLink {
  id: string
  created_at: string
  usaged: number
  limit: number
  is_disabled: boolean
  code: string
}

// 格式化日期
function formatInviteDate(dateString: string) {
  if (!dateString) { return '' }
  const date = new Date(dateString)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 复制邀请码的方法
function copyInviteCode() {
  emits('copy-invite-link', props.item.code)
}

// 切换邀请码状态的方法
function toggleInviteCodeStatus() {
  uni.showModal({
    title: `确认${props.item.is_disabled ? '启用' : '禁用'}`,
    content: `你确定要${props.item.is_disabled ? '启用' : '禁用'}这个邀请码吗？`,
    success: (res) => {
      if (res.confirm) {
        emits('toggle-status', props.item.id, props.item.is_disabled)
      }
      else if (res.cancel) {
        console.log('用户取消操作')
      }
    },
  })
}
function setShare() {
  emits('set-share-prams', '邀请您加入空间', `/pages/workspace/join?scene=${props.item.code}`)
}
// 生成二维码的方法
async function generateQRCode() {
  try {
    const result = await appStore.getQrcode(props.item.code)
    console.log(props.item.code, 'props.item.codeprops.item.codeprops.item.code', result)

    if (result) {
      const base64ImageUrl = `data:image/png;base64,${result}`
      emits('show-qrcode', base64ImageUrl)
    }
  }
  catch (err) {
    console.error(err)
  }
}
</script>

<template>
  <view
    class="m-1  flex flex-col items-center justify-center rounded-xl border-b border-solid border-gray-100 bg-white p-2"
  >
    <view class="flex w-full flex-col text-xs">
      <view>
        <text>邀请码：</text><text>{{ item.code }}</text>
      </view>
      <view>
        <text>创建时间：</text><text>{{ formatInviteDate(item.created_at) }}</text>
      </view>
      <view>
        <text>使用情况：</text><text>
          {{ `${item.usaged}/${item.limit}` }}
        </text>
      </view>
      <view>
        <text>备注：</text><text>
          {{ `${item.remark}` }}
        </text>
      </view>
      <view>
        <text>状态：</text><text>
          {{ item.is_disabled ? '禁用' : '启用' }}
        </text>
      </view>
    </view>
    <view class="mt-3  flex items-center justify-center space-x-2 text-center  text-xs text-gray-900">
      <view>
        <button class="h-8 w-14 rounded-md bg-blue-100 p-2 text-xs text-gray-900 after:border-none" open-type="share" @click="setShare">
          分享
        </button>
      </view>
      <view class="w-14 rounded-md bg-green-100 p-2" @click="copyInviteCode">
        复制
      </view>
      <view class="w-14 rounded-md bg-yellow-100 p-2" @click="generateQRCode">
        二维码
      </view>
      <view class="w-14 rounded-md bg-red-100 p-2" @click="toggleInviteCodeStatus">
        {{ item.is_disabled ? '启用' : '禁用' }}
      </view>
    </view>
  </view>
</template>
