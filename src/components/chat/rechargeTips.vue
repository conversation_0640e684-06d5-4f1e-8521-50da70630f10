<script setup lang="ts">
import { useAppStore } from '@/stores/app'
import { useAuthStore } from '@/stores/auth'
import { ref } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: true,
  },
  appId: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['close'])
const appStore = useAppStore()
const AuthStore = useAuthStore()
const { user, userRoleInfo } = storeToRefs(AuthStore)
const redemptionCode = ref('')
const isVisible = ref(props.visible)

// 粘贴兑换码
async function pasteCode() {
  try {
    const clipboard = await uni.getClipboardData()
    redemptionCode.value = clipboard.data
    uni.showToast({
      title: '粘贴成功',
      icon: 'none',
    })
  }
  catch (_error) {
    uni.showToast({
      title: '粘贴失败',
      icon: 'none',
    })
  }
}

// 获取兑换码
function getRedemptionCode() {
  // 这里可以跳转到获取兑换码的页面或打开相应的弹窗
  uni.navigateTo({
    url: `/pages/pricing/index?appId=${props.appId}&companyId=${userRoleInfo?.value.current_company_id}`,
  })

  // uni.navigateTo({
  //   url: '/subpages/recharge/index',
  // })
}

// 关闭提示窗口
function closeTips() {
  isVisible.value = false
  emit('close')
}

// 确认兑换
async function confirmRedemption() {
  if (!redemptionCode.value) {
    uni.showToast({
      title: '请输入兑换码',
      icon: 'none',
    })
    return
  }

  // 这里添加兑换码验证和兑换逻辑
  const { data, error } = await appStore.useRedemptionCode(user.value.id, props.appId, redemptionCode.value)
  console.log(data, error, 'useRedemptionCode -data')

  if (error) {
    uni.showToast({
      title: error.data.message,
      icon: 'none',
    })
  }
  else {
    uni.showToast({
      title: '兑换成功',
      icon: 'success',
    })
    closeTips()
  }
}
</script>

<template>
  <view v-if="isVisible" class="recharge-tips-container">
    <view class="recharge-tips-card">
      <!-- 关闭按钮 -->
      <view class="close-btn" @click="closeTips">
        ×
      </view>

      <!-- 提示文本 -->
      <view class="tips-header">
        您的次数已达上限! 请使用兑换码兑换会员，兑换后可解锁使用。
      </view>

      <!-- 兑换码输入区域 -->
      <view class="code-input-container">
        <input
          v-model="redemptionCode"
          class="code-input"
          type="text"
          placeholder="请复制兑换码"
        >
        <view class="paste-btn" @click="pasteCode">
          粘贴
        </view>
      </view>

      <!-- 按钮区域 -->
      <view class="action-buttons">
        <view class="get-code-btn" @click="getRedemptionCode">
          获取兑换码
        </view>
        <view class="confirm-btn" @click="confirmRedemption">
          确认兑换
        </view>
      </view>

      <!-- 免责声明 -->
      <view class="disclaimer">
        免责声明：本平台仅提供智能体交互，不涉及金钱交易，请确保自身权益。
      </view>
    </view>
  </view>
</template>

<style scoped>
.recharge-tips-container {
  @apply fixed left-0 right-0 bottom-0 flex justify-center z-40 pb-4 pt-2;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.8) 70%, transparent);
}

.recharge-tips-card {
  @apply w-11/12 max-w-md bg-white rounded-lg p-4 flex flex-col shadow-lg relative;
}

.close-btn {
  @apply absolute top-2 right-2 w-6 h-6 flex items-center justify-center rounded-full text-gray-500 text-xl font-bold cursor-pointer hover:bg-gray-100 hover:text-gray-700 transition-colors;
}

.tips-header {
  @apply text-center text-gray-800 font-medium mb-3;
}

.code-input-container {
  @apply flex items-center border border-gray-200 rounded-lg mb-3 overflow-hidden;
}

.code-input {
  @apply flex-1 h-10 px-3 text-sm;
}

.paste-btn {
  @apply h-10 px-3 flex items-center justify-center text-green-500 text-sm font-medium;
}

.action-buttons {
  @apply flex items-center justify-between mb-3;
}

.get-code-btn {
  @apply flex-1 h-10 bg-amber-500 rounded-lg flex items-center justify-center text-white text-sm font-medium mr-3;
}

.confirm-btn {
  @apply flex-1 h-10 bg-green-500 rounded-lg flex items-center justify-center text-white text-sm font-medium;
}

.disclaimer {
  @apply text-xs text-gray-400 text-center;
}
</style>
