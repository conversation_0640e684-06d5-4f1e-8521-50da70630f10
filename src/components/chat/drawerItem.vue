<script setup lang="ts">
import { useDifyChatStore } from '@/stores/dify-chat'
import { storeToRefs } from 'pinia'

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
})
const emit = defineEmits(['selectInfo'])

// Get the current conversation ID from the store
const difyStore = useDifyChatStore()
const { currentConversationId, isMessagesLoading } = storeToRefs(difyStore)

// Computed property to check if this item is the active conversation
const active = computed(() => {
  return currentConversationId.value === props.item.id
})

function changeMessage(conversation_id: string) {
  emit('selectInfo', conversation_id)
}

function showDelMessage(conversation_id: string) {
  uni.showModal({
    title: '提示',
    content: '确定要删除吗？',
    success: (res) => {
      if (res.confirm) {
        difyStore.deleteConversation(conversation_id)
      }
    },
  })
}
</script>

<template>
  <view class="item" :class="{ 'item-active': active }" @click="changeMessage(props.item.id)" @longpress="showDelMessage(props.item.id)">
    <view class="flex items-center justify-between">
      <view class="truncate pr-2 text-sm text-black">
        {{ props.item.name }}
      </view>
      <!-- Loading indicator when this conversation is being loaded -->
      <view v-if="isMessagesLoading && active" class="loading-spinner">
        <uni-icons type="spinner-cycle" size="16" color="#3b82f6" />
      </view>
    </view>
    <!-- <view class="text-xs text-gray-500 text-right mt-1">{{ props.item.time }}</view> -->
  </view>
</template>

<style scoped>
.item {
  @apply cursor-pointer rounded-md p-2 transition-all duration-200 bg-white mb-2 border border-transparent;
}
.item-active {
  @apply border-blue-500 bg-blue-50 font-medium;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
