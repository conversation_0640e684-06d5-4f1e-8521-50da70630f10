<script setup lang="ts">
import { useDifyChatStore } from '@/stores/dify-chat'
import { globalDataStore } from '@/stores/globalData'

const props = defineProps<{
  title?: string
}>()
const emit = defineEmits(['toggle-drawer', 'setTitleHeight', 'newChat'])
const currentPages = getCurrentPages()
const store = globalDataStore()
const { systemInfo } = storeToRefs(store)
const difyChatStore = useDifyChatStore()
// 获取屏幕宽度
const screenWidth = ref(uni.getSystemInfoSync().screenWidth || 375)
const { appAllInfo } = storeToRefs(difyChatStore)
// 计算标题容器宽度
const leftWidth = ref(110) // 左侧按钮容器宽度
const rightWidth = ref(systemInfo.value?.menuLeft || 40) // 右侧按钮容器宽度
const titleWidth = computed(() => {
  // 计算中间标题区域的宽度 = 屏幕宽度 - 左侧宽度 - 右侧宽度 - 额外边距
  return screenWidth.value - leftWidth.value - rightWidth.value - 20
})

function toggleDrawer() {
  emit('toggle-drawer')
}

function goBack() {
  if (getCurrentPages().length > 1) {
    uni.navigateBack()
  }
  else {
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
}
function newChat() {
  emit('newChat')
}

onMounted(() => {
  // 设置标题栏高度
  emit('setTitleHeight', (systemInfo.value?.navBarHeight || 44) + 44)

  // 获取实际的左右插槽宽度
  nextTick(() => {
    try {
      const query = uni.createSelectorQuery()

      // 获取左侧容器宽度
      query.select('.nav-buttons-container').boundingClientRect((data: any) => {
        if (data && typeof data.width === 'number') {
          leftWidth.value = data.width + 10 // 加上一些边距
        }
      }).exec()

      // 获取右侧容器宽度
      query.select('.right-button').boundingClientRect((data: any) => {
        if (data && typeof data.width === 'number') {
          rightWidth.value = data.width + 10 // 加上一些边距
        }
      }).exec()
    }
    catch (error) {
      console.error('Failed to measure elements:', error)
    }
  })
})
</script>

<template>
  <view class="chat-title-container">
    <uni-nav-bar
      statusBar :border="false" :leftWidth="leftWidth" :rightWidth="rightWidth" backgroundColor="transparent"
      color="#1E40AF" fixed
    >
      <template #left>
        <view class="nav-buttons-container">
          <view class="nav-button home-button" @click="goBack">
            <uni-icons :type="currentPages.length > 1 ? 'left' : 'home'" size="18" color="#3B82F6" />
          </view>
          <view v-if="appAllInfo?.model_type === 'chat' || appAllInfo?.model_type === 'chatflow' " class="nav-button menu-button" @click="toggleDrawer">
            <uni-icons type="list" size="18" color="#3B82F6" />
          </view>
          <view v-if="appAllInfo?.model_type === 'chat' || appAllInfo?.model_type === 'chatflow'" class="nav-button menu-button" @click="newChat">
            <uni-icons fontFamily="CustomFont" size="18" color="3B82F6">
              {{ '\ue6ca' }}
            </uni-icons>
          </view>
        </view>
      </template>

      <template #default>
        <view class="title-container" :style="{ width: `${titleWidth}px` }">
          <view class="title-text">
            {{ props.title || 'AI聊天助手' }}
          </view>
        </view>
      </template>

      <template #right>
        <view class="right-button">
          <uni-icons type="more-filled" size="20" color="#64748B" />
        </view>
      </template>
    </uni-nav-bar>

    <!-- 装饰线条 -->
    <view class="title-decoration" />
  </view>
</template>

<style scoped>
.chat-title-container {
  @apply relative bg-white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.nav-buttons-container {
  @apply flex items-center space-x-1;
}

.nav-button {
  @apply flex h-8 w-8 items-center justify-center rounded-full transition-all duration-200;
}

.home-button {
  @apply bg-blue-50;
}

.home-button:active {
  @apply bg-blue-100;
}

.menu-button {
  @apply bg-blue-50;
}

.menu-button:active {
  @apply bg-blue-100;
}

.title-container {
  @apply flex items-center justify-center;
  max-width: 100%;
  overflow: hidden;
}

.title-text {
  @apply truncate text-base font-medium text-gray-800 pr-2;
  max-width: 100%;
  text-align: center;
}

.right-button {
  @apply flex  items-center justify-center rounded-full transition-all duration-200;
}

.right-button:active {
  @apply bg-gray-100;
}

.title-decoration {
  @apply absolute bottom-0 left-0 right-0 h-0.5;
  background: linear-gradient(to right, #3b82f6, #60a5fa, #3b82f6);
  opacity: 0.8;
}
</style>
