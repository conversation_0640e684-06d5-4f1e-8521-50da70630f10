<script setup lang="ts">
interface Props {
  messagesSuggested: string[]
}

const props = defineProps<Props>()
const emit = defineEmits(['selectQuestion'])

// 处理试着问问点击
function handleQuestionClick(question: string) {
  emit('selectQuestion', question)
  uni.vibrateShort({
    type: 'light',
  })
}
</script>

<template>
  <view v-if="props.messagesSuggested.length > 0" class="suggested-questions">
    <view class="questions-header">
      <view class="header-icon">
        <uni-icons type="help" size="16" color="#6B7280" />
      </view>
      <text class="header-text">试着问问</text>
    </view>

    <view class="questions-list">
      <view
        v-for="(question, index) in props.messagesSuggested"
        :key="index"
        class="question-item"
        @click="handleQuestionClick(question)"
      >
        <view class="question-content">
          <text class="question-text">{{ question }}</text>
        </view>
        <view class="question-arrow">
          <uni-icons type="right" size="14" color="#9CA3AF" />
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped>
.suggested-questions {
  @apply mx-4 mb-4 rounded-xl bg-gray-50 p-4;
  border: 1px solid #f3f4f6;
}

.questions-header {
  @apply mb-3 flex items-center;
}

.header-icon {
  @apply mr-2;
}

.header-text {
  @apply text-sm font-medium text-gray-600;
}

.questions-list {
  @apply space-y-2;
}

.question-item {
  @apply flex cursor-pointer items-center justify-between rounded-lg bg-white p-3 transition-all duration-200;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.question-item:active {
  @apply bg-blue-50;
  border-color: #3b82f6;
  transform: scale(0.98);
}

.question-content {
  @apply flex-1;
}

.question-text {
  @apply text-sm text-gray-700;
  line-height: 1.4;
  word-break: break-word;
}

.question-arrow {
  @apply ml-2 flex-shrink-0;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .suggested-questions {
    @apply mx-3 p-3;
  }

  .question-item {
    @apply p-2.5;
  }

  .question-text {
    @apply text-xs;
  }
}

/* 动画效果 */
.question-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为不同索引的问题添加延迟动画 */
.question-item:nth-child(1) {
  animation-delay: 0.1s;
}
.question-item:nth-child(2) {
  animation-delay: 0.2s;
}
.question-item:nth-child(3) {
  animation-delay: 0.3s;
}
.question-item:nth-child(4) {
  animation-delay: 0.4s;
}
.question-item:nth-child(5) {
  animation-delay: 0.5s;
}
</style>
