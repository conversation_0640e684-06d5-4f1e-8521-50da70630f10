<script setup lang="ts">
import { useDifyChatStore } from '@/stores/dify-chat'

import DrawerItem from './drawerItem.vue'

const emit = defineEmits(['closeDrawer', 'newChat'])

const touchStartX = ref(0)
const touchMoveX = ref(0)
const difyStore = useDifyChatStore()
const {
  conversationHistory,
  currentConversationId,
  hasMoreHistory,
  isLoadingHistory,
  isLoadingApp,
} = storeToRefs(difyStore)
onMounted(() => {

})
function handleCloseDrawer() {
  emit('closeDrawer')
}
function handleNewChat() {
  emit('newChat')
  emit('closeDrawer')
}

// 处理触摸开始事件
function handleTouchStart(event: TouchEvent) {
  touchStartX.value = event.touches[0].clientX
}

// 处理触摸移动事件
function handleTouchMove(event: TouchEvent) {
  touchMoveX.value = event.touches[0].clientX
  const diffX = touchMoveX.value - touchStartX.value

  // 从右向左滑动超过50px时关闭抽屉
  if (diffX < -100) {
    handleCloseDrawer()
  }
}

async function handleSelectInfo(conversation_id: string) {
  try {
    // 如果点击的是当前对话，不做任何操作
    if (conversation_id === currentConversationId.value) { return }

    difyStore.clearMessages()

    await difyStore.getMessages(conversation_id)

    // 触发选择事件
    emit('closeDrawer')
  }
  catch (error) {
    console.error('Failed to load conversation:', error)
  }
};

// 处理滚动加载
async function handleScroll(e: Event) {
  // 当滚动到底部时加载更多、
  if (hasMoreHistory.value) {
    await difyStore.loadConversations(true)
  }
}
</script>

<template>
  <!-- 抽屉内容保持不变 -->
  <view class="flex h-screen flex-col" @touchstart="handleTouchStart" @touchmove="handleTouchMove">
    <view class="h-24">
      <uni-nav-bar left-icon="left" statusBar :border="false" :fixed="true">
        <template #left class="w-16">
          <view class="flex flex-row items-center justify-center">
            <uni-icons type="left" size="26" @click="handleCloseDrawer" />
          </view>
        </template>
        <template #default class="w-16">
          <view class="flex w-full items-center justify-center text-center text-base">
            会话记录
          </view>
        </template>
      </uni-nav-bar>
    </view>

    <!-- 功能菜单区域 -->
    <div class="border-b border-gray-200 p-4">
      <h3 class="mb-2 text-sm font-medium text-gray-500">
        功能菜单
      </h3>
      <ul class="space-y-1">
        <li
          class="flex cursor-pointer items-center rounded-md p-2 transition-all duration-200 hover:bg-blue-50"
          @click="handleNewChat"
        >
          <uni-icons type="plus" size="18" color="#3b82f6" class="mr-2" />
          <span>新建聊天</span>
        </li>
      </ul>
    </div>

    <!-- 聊天列表区域 -->
    <scroll-view class="flex-auto grow overflow-hidden " scroll-y="true" @scrolltolower="handleScroll">
      <view class="flex-1 p-4 pb-10">
        <view class="mb-2 flex items-center justify-between">
          <view class="text-sm font-medium text-gray-500">
            聊天列表
          </view>
        </view>

        <view class="flex flex-col space-y-2">
          <DrawerItem
            v-for="item in conversationHistory" :key="item.id" :item="item" class="mb-2"
            @selectInfo="handleSelectInfo"
          />
          <uni-load-more :status="hasMoreHistory ? 'more' : isLoadingHistory ? 'loading' : 'noMore'" />
        </view>
      </view>
    </scroll-view>
  </view>
</template>
