<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'
import { useDifyChatStore } from '@/stores/dify-chat'
import { globalDataStore } from '@/stores/globalData'
import { useMemoryStore } from '@/stores/memory'
import { ref } from 'vue'

const emit = defineEmits(['send', 'setHeight', 'selectQuestion'])
const store = globalDataStore()
const { Platform } = storeToRefs(store)

// #ifdef MP-WEIXIN
const authStore = useAuthStore()
const plugin = requirePlugin('WechatSI') // 微信同声传译插件[语音输入]
const manager = plugin.getRecordRecognitionManager()
// #endif
const { userRoleInfo, user } = storeToRefs(authStore)
const message = ref('')
const memoryTemp = ref('')
const memoryStore = useMemoryStore()
const isVoice = ref(false)

const TouchData = reactive({ // 语音触摸数据
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0,
})
const difyStore = useDifyChatStore()
const voiceState = ref(false) // 语音状态
const voiceTips = ref() // 录音提示窗口

const recordStatus = ref(false) // 录音状态
const touchStatus = ref(false) // 触摸状态
const inputStatus = ref(false) // 输入状态
// const isLoadingMessage = ref(false) // 发送状态
const keyboardHeight = ref(0) // 键盘抬起高度
const keyboardDuration = ref('') // 键盘抬起延迟

const {
  isLoadingMessage, // 是否正在加载消息
  userInputs, // 记忆体
  sendMmoryStatus,
  messagesSuggested,

} = storeToRefs(difyStore)
const { MemoryList } = storeToRefs(memoryStore)

function handleSend() {
  if (isLoadingMessage.value) {
    // 停止请求

    difyStore.stopResponse()

    isLoadingMessage.value = false
    return
  }

  if (!message.value.trim()) { return }
  emit('send', message.value)
  uni.vibrateShort({
    type: 'medium',
    success() {
      console.log('success')
    },
  })
  message.value = ''
  // isLoadingMessage.value = true
}
function lineChange(event: any) {

}

function changeVoice(event) {
  keyboardHeight.value = 0

  if (isLoadingMessage.value) { return }
  isVoice.value = !isVoice.value
  uni.vibrateShort({
    type: 'medium',
    success() {
      console.log('success')
    },
  })
  console.log(event, 'changeVoice', isVoice.value)
}

function handleVoiceStart(event: any) {
  uni.vibrateShort({
    type: 'medium',
    success() {
      console.log('success')
    },
  })
  // TouchData.startY = event.touches[0].clientY // 手指按下时的Y坐标
  manager.start({
    duration: 60000, // 指定录音的时长，单位ms，最大为60000
    lang: 'zh_CN',
  })

  console.log(event, 'handleVoiceStart')
  if (event.changedTouches) {
    TouchData.startY = event.changedTouches[0]?.clientY // 手指按下时的Y坐标
  }
}

function handleVoiceEnd(event) {
  manager.stop()

  voiceTips.value.close()
  voiceState.value = false

  console.log('handleVoiceEnd', event)
  touchStatus.value = false
}

function handleVoiceMove(event) {
  // 触摸后继续移动手指时，将触发这个事件
  // 这里我们根据每次移动的坐标变化判断是向上还是向下滑动
  let touchData = event.touches[0] // 滑动过程中，手指滑动的坐标信息 返回的是Objcet对象

  let moveY = touchData.clientY - TouchData.startY
  console.log(moveY, 'moveYmoveY')

  if (moveY < -30) {
    // 向上滑动
    recordStatus.value = false
  }
  else {
    // 向下滑动
    recordStatus.value = true
  }
}
function handleBlur() {
  console.log(message.value.length, 'message.value.lengthmessage.value.lengthmessage.value.length')
  keyboardHeight.value = 0
  if (message.value.length === 0) {
    inputStatus.value = false
  }
}

function handleFocus(event: any) {
  if (isLoadingMessage.value) { return }
  inputStatus.value = true
}

function handleKeyboardHeightChange(event: any) {
  console.log(keyboardHeight.value, event.detail.height, 'before')

  keyboardHeight.value = event.detail.height
  console.log(keyboardHeight.value, event.detail.height, 'after')
  keyboardDuration.value = event.detail.duration
}

function initRecord() { // 微信同声传译插件[语音输入]
  manager.onStart = function (res: any) { // 正常开始录音识别时会调用此事件
    console.log('onStart result AA', res)

    touchStatus.value = true // 触摸状态 true
    voiceTips.value.open() // 打开提示

    recordStatus.value = true
    voiceState.value = true
  }

  manager.onStop = (res: any) => { // 识别结束事件
    console.log('onStop file path CC', res.tempFilePath)
    console.log('onStop DD', res)

    console.log(recordStatus.value && res.result, 'recordStatus.value && res.result', recordStatus.value, res.result)

    if (recordStatus.value && res.result !== '') {
      message.value = res.result
      isVoice.value = false
    }
  }
  manager.onError = (res: any) => { // 识别错误事件
    console.warn(res, 'onError')
    console.log(typeof res.retcode, 'res.coderes.code')

    switch (res.retcode) {
      case -30004:
        uni.showToast({
          title: '听不清楚，请重新说一遍！',
          icon: 'none',
        })
        break
      case -30011:
        uni.showToast({
          title: '等待返回',
          icon: 'none',
        })
        break
    }
    voiceState.value = false
    recordStatus.value = false
  }
}

async function handleMemoryToggle(e: any) {
  if (e.type === 'click' || e.type === 'tap') {
    uni.vibrateShort({
      type: 'medium',
      success() {
        console.log('success')
      },
    })
    const newValue = !userRoleInfo.value.is_use_memory

    try {
      await memoryStore.companyUpdateUserMemorySetting(newValue)

      userRoleInfo.value.is_use_memory = newValue
      authStore.checkUserInfo(user.value.id)

      MemorySetting()
    }
    catch (res) {
      console.log(res, 'error')
    }
  }
}
function MemorySetting() {
  if (userRoleInfo?.value.is_use_memory) {
    for (const item of MemoryList?.value.list) {
      if (item.key !== '' && item.value !== '') {
        memoryTemp.value = `${memoryTemp.value}${item.key}:${item.value}\n`
      }
      else if (item.value !== '') {
        memoryTemp.value = `${memoryTemp.value}${item.value}\n`
      }
    }

    userInputs.value = { memory: memoryTemp.value }
  }
  else {
    memoryTemp.value = ''
    userInputs.value = {}
  }
  console.log(userInputs.value, 'memoryTemp')
}

// 处理建议问题点击
function handleQuestionClick(question: string) {
  emit('selectQuestion', question)
  uni.vibrateShort({
    type: 'light',
  })
}

onMounted(async () => {
  // #ifdef MP-WEIXIN
  initRecord()

  // #endif
  MemorySetting()
})
onUnmounted(() => {
  messagesSuggested.value = []
  manager.stop()
})
</script>

<template>
  <view
    id="sendBox" class="message-sender"
    :style="{ transform: keyboardHeight > 0 ? `translateY(-${keyboardHeight}px)` : 'translateY(0px)' }"
    :class="`duration-[${keyboardDuration}s]`"
  >
    <!-- 建议问题区域 -->
    <view v-if="messagesSuggested.length > 0" class="suggested-questions">
      <view class="mb-1 text-sm">
        试着问问:
      </view>
      <view class="questions-container">
        <view
          v-for="(question, index) in messagesSuggested" :key="index" class="question-pill"
          @click="handleQuestionClick(question)"
        >
          <text class="question-text">{{ question }}</text>
        </view>
      </view>
    </view>

    <view class="input-container" :class="inputStatus && !isVoice ? 'input-container-active' : ''">
      <!-- 记忆按钮 (非输入状态) -->

      <view v-if="sendMmoryStatus">
        <button
          v-if="(!inputStatus && !isVoice)" class="memory-button after:border-none"
          :class="userRoleInfo.is_use_memory ? 'active' : ''" @click="handleMemoryToggle"
        >
          <uni-icons fontFamily="CustomFont" size="16" :color="userRoleInfo.is_use_memory ? '#fff' : '#3b82f6'">
            {{ '\ue618' }}
          </uni-icons>
          <view class="memory-text">
            记忆
          </view>
        </button>
      </view>

      <!-- 输入区域 -->
      <view class="textarea-container">
        <!-- 文字输入模式 -->
        <view v-if="!isVoice" class="text-input-mode">
          <textarea
            v-model="message" type="text" placeholder="输入消息..." placeholder-style="color: #94A3B8;"
            class="message-textarea" :auto-height="true" :adjust-position="false" :fixed="true" maxlength="99999"
            :show-confirm-bar="false" confirm-type="send" cursor="message.length" @linechange="lineChange"
            @focus="handleFocus" @blur="handleBlur" @confirm="handleSend"
            @keyboardheightchange="handleKeyboardHeightChange"
          />
        </view>

        <!-- 语音输入模式 -->
        <view v-else class="voice-input-mode">
          <button
            class="voice-button" :class="{
              recording: touchStatus && recordStatus,
              cancel: touchStatus && !recordStatus,
            }" @touchend="handleVoiceEnd" @touchstart="handleVoiceStart" @touchmove="handleVoiceMove"
          >
            {{ !touchStatus ? '按住说话' : recordStatus ? '正在聆听' : '松开取消' }}
          </button>
        </view>
      </view>

      <!-- 控制区域 -->
      <view class="control-container" :class="sendMmoryStatus ? 'justify-between' : 'items-end justify-end'">
        <!-- 记忆按钮 (输入状态) -->
        <view v-if="sendMmoryStatus">
          <button
            v-if="inputStatus && !isVoice" class="memory-button "
            :class="userRoleInfo.is_use_memory ? 'active' : ''" @click="handleMemoryToggle"
          >
            <uni-icons fontFamily="CustomFont" size="16" :color="userRoleInfo.is_use_memory ? '#fff' : '#3b82f6'">
              {{ '\ue618' }}
            </uni-icons>
            <view class="memory-text">
              记忆
            </view>
          </button>
        </view>

        <!-- 功能按钮区 -->
        <view class="action-buttons" :class="sendMmoryStatus ? '' : 'items-end justify-end'">
          <!-- 语音/文字切换按钮 -->

          <button
            v-if="Platform !== 'h5' && Platform !== 'web'" class="action-button voice-toggle"
            @click="changeVoice"
          >
            <uni-icons :type="isVoice ? 'font' : 'mic'" size="20" color="#3B82F6" />
          </button>

          <!-- 发送按钮 -->
          <button class="action-button send-button " @click="handleSend">
            <uni-icons :type="isLoadingMessage ? 'spinner-cycle' : 'paperplane-filled'" size="20" color="#FFFFFF" />
          </button>
        </view>
      </view>
    </view>
  </view>

  <!-- 语音提示窗口 -->
  <uni-popup ref="voiceTips" type="dialog" mask-background-color="rgba(0,0,0,0)">
    <view class="voice-popup">
      <view class="voice-popup-icon">
        <img class="voice-icon" src="../../static/images/voice.png">
      </view>
      <view class="voice-popup-text">
        {{ !touchStatus ? '按住说话' : recordStatus ? '正在聆听' : '松开取消' }}
      </view>
    </view>
  </uni-popup>
</template>

<style scoped>
/* 消息发送器容器 */
.message-sender {
  @apply fixed bottom-0 left-0 w-full transform-gpu flex-col items-center rounded-t-xl bg-white p-3 pb-6 transition-all;
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
  max-height: 24rem;
  min-height: 5rem;
  z-index: 10;
}

/* 建议问题区域 */
.suggested-questions {
  @apply w-full mb-3;
}

.questions-container {
  @apply flex flex-wrap gap-2 justify-end;
}

.question-pill {
  @apply cursor-pointer rounded-full bg-gray-100 px-3 py-1.5 transition-all duration-200;
  border: 1px solid #e5e7eb;
  max-width: calc(100% - 0.5rem);
}

.question-pill:active {
  @apply bg-blue-50;
  border-color: #3b82f6;
  transform: scale(0.95);
}

.question-text {
  @apply text-sm text-gray-700;
  line-height: 1.2;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 输入容器 */
.input-container {
  @apply flex min-h-12 flex-row items-center justify-between rounded-xl bg-gray-50 p-2 w-auto;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.2s ease;
}

/* 输入容器激活状态 */
.input-container-active {
  @apply flex-col;
  background-color: white;
  border-color: #3b82f6;
}

/* 记忆按钮 (非输入状态) */
.memory-button {
  @apply flex w-16 h-7 cursor-pointer items-center justify-center rounded-xl border border-solid border-blue-100 bg-blue-50 p-1 after:border-none;
}

/* 记忆按钮文字 */
.memory-text {
  @apply pl-1 text-xs text-blue-600 font-medium;
}

/* 记忆按钮 (输入状态) */
.memory-button.active {
  @apply border-blue-600 bg-blue-500;
}

.memory-button.active .memory-text {
  @apply text-white;
}

/* 输入区域容器 */
.textarea-container {
  @apply flex-auto px-2;
}

/* 文字输入模式 */
.text-input-mode {
  @apply w-full;
}

/* 消息文本区 */
.message-textarea {
  @apply max-h-20 w-full max-w-full focus:outline-none;
  min-height: 1.5rem;
}

/* 语音输入模式 */
.voice-input-mode {
  @apply h-12 flex-auto;
}

/* 语音按钮 */
.voice-button {
  @apply mx-2 flex h-full w-full items-center justify-center rounded-xl bg-blue-500 text-sm text-white p-0;
  transition: all 0.2s ease;
}

/* 录音中状态 */
.voice-button.recording {
  @apply bg-green-500;
}

/* 取消状态 */
.voice-button.cancel {
  @apply bg-red-500;
}

/* 控制区域容器 */
.control-container {
  @apply flex items-center;
}

/* 输入状态下的控制区域 */
.input-container-active .control-container {
  @apply w-full pt-2;
}

/* 输入状态下的输入区域 */
.input-container-active .textarea-container {
  @apply w-full;
}

/* 功能按钮区 */
.action-buttons {
  @apply flex;
}

/* 功能按钮 */
.action-button {
  @apply flex h-9 w-9 items-center justify-center rounded-full transition-all duration-200 after:border-none;
}

/* 语音切换按钮 */
.voice-toggle {
  @apply bg-blue-50;
}

.voice-toggle:active {
  @apply bg-blue-100;
}

/* 发送按钮 */
.send-button {
  @apply ml-2 bg-blue-500;
}

.send-button:active {
  @apply bg-blue-600;
}

/* 语音提示窗口 */
.voice-popup {
  @apply flex h-32 w-32 flex-col items-center justify-center rounded-lg bg-black/80;
}

.voice-popup-icon {
  @apply flex items-center justify-center;
}

.voice-icon {
  @apply m-auto block h-16 w-16;
}

.voice-popup-text {
  @apply mt-2 text-sm text-white;
}
</style>
