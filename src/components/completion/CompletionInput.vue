<script setup lang="ts">
import CompletionSaved from '@/components/completion/CompletionSaved.vue'

import DifyForm from '@/components/inputs/types/DifyForm.vue'
import { useDifyChatStore } from '@/stores/dify-chat'

const emit = defineEmits(['openDrawer'])
const keyboardHeight = ref(0)
const difyChatStore = useDifyChatStore()
const {
  currentApp,
  appParameters,
  isLoadingMessage,
  userInputs,
  isLoadingApp,
  completionSavedList,
  type,
  appAllInfo,
} = storeToRefs(difyChatStore)

const activeTab = ref('run')
const userInputFormList = computed(() => {
  const list: any[] = [];
  // console.log(appParameters, 'appParametersappParameters');

  (appParameters.value?.user_input_form || []).forEach((field) => {
    Object.entries(field).forEach(([key, value]) => {
      if (value && typeof value === 'object') {
        value.type = value.type || key
        list.push(value)
      }
    })
  })
  return list
})

function clearForm() {
  userInputs.value = {}
}

function runGeneration() {
  uni.vibrateShort({
    type: 'medium',
    success() {
      console.log('success')
    },
  })
  if (isLoadingMessage.value) {
    return
  }
  if (type.value === 'workflow') {
    difyChatStore.startWorkflow()
  }
  else {
    difyChatStore.startTextGeneration()
  }
}

function handleSwitchToRun() {
  activeTab.value = 'run'
}

function openDrawer() {
  emit('openDrawer')
}
</script>

<template>
  <view class="flex size-full flex-col">
    <view class="mb-2 flex items-center px-4">
      <view class="flex items-center gap-2 p-2">
        <view class="flex size-8 items-center justify-center rounded-xl ">
          <!-- <text>{{ appAllInfo?.icon }}</text> -->
          <img :src="appAllInfo?.icon" class="h-full object-contain">
        </view>
        <text class="text-base font-semibold">
          {{ appAllInfo?.name || "文本生成型应用" }}
        </text>
      </view>
      <view class="ml-auto">
        <!-- #ifdef H5 || MP-WEIXIN -->
        <button
          class="flex h-8 w-28  items-center justify-center border-none bg-blue-500 text-xs text-white after:border-none"
          @click="openDrawer"
        >
          <uni-icons type="list" size="12" color="#fff" />
          <view>AI 智能书写</view>
        </button>
        <!-- #endif -->
      </view>
    </view>

    <view class="flex w-full grow flex-col overflow-y-hidden">
      <!-- 标签切换区域 -->
      <view class="grid w-full grid-cols-2 border-b border-gray-200 px-4">
        <view
          class="relative flex items-center justify-center py-2"
          :class="activeTab === 'run' ? 'text-blue-600 font-medium' : 'text-gray-500'" @click="activeTab = 'run'"
        >
          <text>运行</text>
          <view v-if="activeTab === 'run'" class="absolute bottom-0 left-0 h-0.5 w-full bg-blue-600" />
        </view>
        <view
          class="relative flex items-center justify-center py-2"
          :class="activeTab === 'saved' ? 'text-blue-600 font-medium' : 'text-gray-500'" @click="activeTab = 'saved'"
        >
          <text>已保存</text>
          <view v-if="completionSavedList.length" class="ml-1 rounded-md bg-gray-100 px-2 py-0.5 text-xs text-gray-700">
            <text>{{ completionSavedList.length }}</text>
          </view>
          <view v-if="activeTab === 'saved'" class="absolute bottom-0 left-0 h-0.5 w-full bg-blue-600" />
        </view>
      </view>

      <!-- 运行内容区域 -->
      <view v-if="activeTab === 'run'" class="mb-10 flex grow flex-col justify-between overflow-y-auto p-3">
        <view class="rounded-lg border border-gray-200 bg-white shadow-sm" :style="`margin-bottom:${keyboardHeight}`">
          <view class="space-y-6 p-2">
            <view v-if="isLoadingApp">
              <view class="space-y-4">
                <!-- 骨架屏 -->
                <view class="mb-2 h-4 w-1/4 animate-pulse rounded bg-gray-200" />
                <view class="h-10 animate-pulse rounded bg-gray-200" />
                <view class="mb-2 h-4 w-1/4 animate-pulse rounded bg-gray-200" />
                <view class="h-10 animate-pulse rounded bg-gray-200" />
                <view class="mb-2 h-4 w-1/4 animate-pulse rounded bg-gray-200" />
                <view class="h-10 animate-pulse rounded bg-gray-200" />
              </view>
            </view>
            <view v-else-if="userInputFormList.length > 0">
              <DifyForm layout="vertical" @keyboardHeight="keyboardHeight" />
            </view>
            <view class="flex justify-between gap-4">
              <view
                class="flex items-center justify-center rounded-md border border-gray-300 px-4 py-2 text-sm"
                hover-class="bg-gray-100" @click="clearForm"
              >
                <text>清空</text>
              </view>
              <view
                class="flex items-center justify-center rounded-md bg-blue-600 px-4 py-2 text-sm text-white"
                :class="isLoadingMessage ? 'opacity-70' : ''" hover-class="bg-blue-700" :disabled="isLoadingMessage"
                @click="runGeneration"
              >
                <view v-if="isLoadingMessage" class="mr-1 size-4">
                  <uni-icons type="spinner-cycle" size="20" color="#FFFFFF" />
                </view>
                <view v-else class="mr-1 size-4">
                  <uni-icons type="chatbubble-filled" size="20" color="#FFFFFF" />
                </view>
                <text>{{ isLoadingMessage ? "生成中..." : "运行" }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- <ChatUserTokenBar /> -->
      </view>

      <!-- 已保存内容区域 -->
      <view v-if="activeTab === 'saved'" class="grow overflow-y-auto">
        <CompletionSaved @switchToRun="handleSwitchToRun" />
      </view>
    </view>
  </view>
</template>
