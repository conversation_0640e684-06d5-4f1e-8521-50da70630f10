<script setup lang="ts">
// import VerResponse from '@/components/ver-response/ver-response/ver-response.vue'
import UaMarkdown from '@/components/ua-markdown/ua-markdown.vue'

const props = defineProps<{
  id: string
  content: string
  created_at: string
}>()

const emit = defineEmits<{
  delete: [id: string]
}>()

const contentRef = ref<HTMLElement | null>(null)
const expanded = ref(false)
const shouldShowExpand = ref(true)

// 检查内容高度的函数
function checkContentHeight() {
  // console.log(contentRef.value.scrollHeight, 'contentRef.value.scrollHeightcontentRef.value.scrollHeight')

  // nextTick(() => {
  //   if (contentRef.value) {
  //     shouldShowExpand.value = contentRef.value.scrollHeight > 320
  //   }
  // })
}

// 监听内容变化
watchEffect(checkContentHeight)

// 组件挂载时也检查一次
onMounted(checkContentHeight)

async function copyContent() {
  try {
    // text = text.replace(/>\s*💭.*\n>/gs, '').trim()
    uni.setClipboardData({
      data: props.content,
      success: () => {
        uni.showToast({
          title: '复制成功',
          icon: 'none',
        })
      },
    })
  }
  catch (error) {
    uni.showToast({
      title: '无法复制内容',
      duration: 2000,
    })
  }
}
</script>

<template>
  <view class="m-auto px-1">
    <uni-card :is-shadow="false">
      <view class="pt-1">
        <!-- Content -->
        <div ref="contentRef" :class="{ 'max-h-[320px] overflow-hidden': !expanded && shouldShowExpand }">
          <!-- <VerResponse theme="vsinger" :content="content" themeColor="#3B82F6" /> -->
          <UaMarkdown :source="content" />
        </div>

        <!-- Expand/Collapse Button -->
        <div v-if="shouldShowExpand" class="mt-4 text-center">
          <button class="w-20 bg-transparent text-xs after:border-none" @click="expanded = !expanded">
            {{ expanded ? "收起" : "展开" }}
            <uni-icons v-if="!expanded" type="down" size="16" color="#000" />
            <uni-icons v-else type="up" size="16" color="#000" />
          </button>
        </div>

        <!-- Footer -->
        <template #actions>
          <div class="mt-4 flex items-center justify-between border-t pt-4">
            <div class="text-sm text-gray-500">
              {{ new Date(created_at).toLocaleString() }}
            </div>
            <div class="flex items-center gap-3">
              <button
                class="flex h-8 w-16 items-center justify-center gap-2 bg-blue-600 text-sm text-white"
                @click="copyContent"
              >
                <!-- <uni-icons fontFamily="CustomFont" size="16" color="#fff">
                  {{ '\ue63d' }}
                </uni-icons> -->
                <view>
                  复制
                </view>
              </button>

              <button
                class="flex h-8 w-16 items-center justify-center gap-2 bg-blue-600 text-sm text-white"
                @click="$emit('delete', id)"
              >
                <!-- <uni-icons type="closeempty" size="16" color="#fff" /> -->
                <view>
                  删除
                </view>
              </button>
            </div>
          </div>
        </template>
      </view>
    </uni-card>
  </view>
</template>
