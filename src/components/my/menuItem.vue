<script setup lang="ts">
import type { MenuItem } from '@/types/menu'

import { useAuthStore } from '@/stores/auth'

const { item } = defineProps<{
  item: MenuItem
}>()

const emit = defineEmits(['bind-phone', 'bind-wechat', 'change-check-type'])

const authStore = useAuthStore()

const { loginStatus, user } = storeToRefs(authStore)

function navigateTo(item: any) {
  if (item.isLogin && loginStatus.value === false) {
    uni.navigateTo({
      url: '/pages/auth/login',
    })
    return
  }
  switch (item.type) {
    case 'share':

      break
    case 'page':
      uni.navigateTo({
        url: item.path,
      })
      break
    default:
      break
  }
}

function handleClick(item: MenuItem) {
  if (!item.func) { return }

  if (item.id === 'create-workspace') {
    emit('change-check-type', 'create')
  }
  else if (item.id === 'join-workspace') {
    emit('change-check-type', 'join')
  }

  switch (item.func) {
    case 'checkInfo':
      if (loginStatus.value === false) {
        uni.navigateTo({
          url: '/pages/auth/login',
        })
      }
      else if (!user?.value?.phone || user?.value?.phone === '' || user?.value?.phone === 'null') {
        emit('bind-phone')
      }
      else {
        uni.navigateTo({
          url: item.path || '',
        })
      }
      break

    case 'bindWx':
      if (loginStatus.value === false) {
        uni.navigateTo({
          url: '/pages/auth/login',
        })
      }
      else {
        emit('bind-wechat')
      }
      break

    default:
      break
  }
}
function changeCheckType(type: string) {
  emit('change-check-type', type)
}
</script>

<template>
  <view class="menu-item mx-4 mb-2 flex items-center rounded-lg  bg-white  py-3" @click="navigateTo(item)">
    <uni-icons :type="item.icon" size="20" :color="item.iconColor" class="mx-3" />
    <button
      v-if="item.type === 'share'"
      class="item-label flex-1  bg-white text-left  text-base text-gray-800 after:border-none"
      :open-type="`${item.type === 'share' ? 'share' : ''}`"
    >
      {{ item.label }}
    </button>
    <button
      v-if="item.type === 'func'"
      class="item-label flex-1  bg-white text-left  text-base text-gray-800 after:border-none"
      @click.stop="handleClick(item)"
    >
      {{ item.label }}
    </button>
    <button
      v-if="item.type === 'page'"
      class="item-label flex-1  bg-white text-left  text-base text-gray-800 after:border-none"
    >
      {{ item.label }}
    </button>
    <uni-icons type="right" size="12" color="#9CA3AF" class="mr-5" />
  </view>
</template>
