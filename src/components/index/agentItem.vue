<script setup lang="ts">
import type { Agent } from '@/types/agent'
import { useAgentStore } from '@/stores/agent'
import { useAuthStore } from '@/stores/auth'
import { computed } from 'vue'

const props = defineProps<{
  agentItem: Agent
}>()
const emit = defineEmits(['reLoad'])
const agentStore = useAgentStore()
const authStore = useAuthStore()
const { userRoleInfo } = storeToRefs(authStore)

// 新增计算属性，判断是否显示编辑按钮
function shouldShowEditButton() {
  return userRoleInfo?.value.is_admin || userRoleInfo?.value.is_owner
}

function navigateToAgentDetail(agentType: string) {
  switch (agentType) {
    case 'chat':
      uni.navigateTo({
        url: `/pages/agent/chat?id=${props.agentItem.id}&agentName=${props.agentItem.name}&type=${props.agentItem.model_type}`,
      })
      break
    case 'completion':
      uni.navigateTo({
        url: `/pages/agent/completion?id=${props.agentItem.id}&agentName=${props.agentItem.name}&type=${props.agentItem.model_type}`,
      })
      break
    case 'workflow':
      uni.navigateTo({
        url: `/pages/agent/workflow?id=${props.agentItem.id}&agentName=${props.agentItem.name}&type=${props.agentItem.model_type}`,
      })
      break
    default:
      uni.navigateTo({
        url: `/pages/agent/chat?id=${props.agentItem.id}&agentName=${props.agentItem.name}&type=${props.agentItem.model_type}`,
      })
      break
  }
}

async function toggleFavorite() {
  console.log('传过去的id', `/pages/agent/create?appId=${props.agentItem.id}`)
  uni.navigateTo({
    url: `/pages/agent/create?appId=${props.agentItem.id}`,
  })
  // const newFavoriteStatus = !props.agentItem.is_favorite
  // 这里应该调用store中的方法来切换收藏状态
  // await agentStore.favoriteApp(props.agentItem.id, newFavoriteStatus)
  // emit('reLoad')
}

function shareAgent() {
  // 实现分享功能
  // eslint-disable-next-line no-console
  console.log('Share agent:', props.agentItem.id)
}
</script>

<template>
  <view class="agent-card w-full" @click="navigateToAgentDetail(agentItem.model_type)">
    <!-- 头像区域 -->
    <view class="avatar-container">
      <image v-if="agentItem.icon" :src="agentItem.icon" class="avatar-image" />
      <text v-else class="avatar-placeholder">{{ agentItem.name.charAt(0) }}</text>
    </view>

    <!-- 内容区域 -->
    <view class="content-container">
      <view class="agent-name" :class="{ 'long-name': agentItem.name.length > 10 }">
        {{ agentItem.name }}
      </view>
      <view class="agent-description" :class="{ 'long-description': agentItem.description.length > 10 }">
        {{ agentItem.description }}
      </view>
    </view>
    <view />

    <!-- 操作区域 -->
    <view class="actions-container">
      <!-- 根据 shouldShowEditButton 判断是否显示编辑图标 -->
      <view v-if="shouldShowEditButton" class="action-button edit" @click="toggleFavorite">
        <uni-icons type="more-filled" size="15" class="m-auto mr-4" color="#9CA3AF" @click="toggleFavorite" />
      </view>
      <view class="action-button share" @click="shareAgent">
        <uni-icons type="redo" size="18" color="#94A3B8" />
      </view>
    </view>
  </view>
</template>

<style scoped>
.agent-card {
  @apply flex items-center rounded-xl bg-gray-50  shadow-sm transition-all duration-200;
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 1px 2px rgba(0, 0, 0, 0.03);
}

.agent-card:active {
  @apply bg-blue-50;
  transform: scale(0.99);
}

.avatar-container {
  @apply mr-3 flex items-center justify-center overflow-hidden rounded-l-lg size-16;
}

.avatar-image {
  @apply h-full w-full object-cover;
}

.avatar-placeholder {
  @apply text-xl font-bold text-white;
}

.content-container {
  @apply flex-1 overflow-hidden;
}

.agent-name {
  @apply mb-1 truncate text-base font-medium text-gray-900 w-48;
}

.long-name {
  @apply text-sm;
}

.agent-description {
  @apply line-clamp-2 text-sm text-gray-500 w-48;
}
.long-description {
  @apply text-xs;
}

.actions-container {
  @apply flex items-center;
}

.action-button {
  @apply ml-2 flex size-8 items-center justify-center rounded-full transition-all duration-200;
}

.action-button:active {
  @apply bg-gray-100;
}

/* 添加编辑图标的样式类（如果需要） */
.edit {
  /* 可以在这里添加额外的样式 */
}
</style>
