<script setup lang="ts">
function navigateToRecharge() {
  uni.navigateTo({
    url: '/subpages/recharge/index',
  })
}
</script>

<template>
  <view class="balance-alert-container">
    <view class="balance-alert">
      <view class="alert-icon">
        <uni-icons type="notification" size="16" color="#FFFFFF" />
      </view>
      <!-- <view class="alert-content">
        <text class="alert-text">您账户下电力值余ddd额不多</text>
      </view> -->
      <!-- <view class="alert-action" @click="navigateToRecharge">
        <text class="action-text">立即充值</text>
        <uni-icons type="arrowright" size="12" color="#FFFFFF" class="ml-1" />
      </view> -->
    </view>
  </view>
</template>

<style scoped>
.balance-alert-container {
  @apply px-4 py-3;
}

.balance-alert {
  @apply flex items-center rounded-lg p-3 relative overflow-hidden;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.2);
}

.alert-icon {
  @apply flex items-center justify-center rounded-full bg-white/20 p-1.5 mr-3;
}

.alert-content {
  @apply flex-1;
}

.alert-text {
  @apply text-sm text-white font-medium;
}

.alert-action {
  @apply flex items-center bg-white/20 rounded-full px-3 py-1.5;
  transition: all 0.2s ease;
}

.alert-action:active {
  @apply bg-white/30;
}

.action-text {
  @apply text-xs text-white font-medium;
}
</style>
