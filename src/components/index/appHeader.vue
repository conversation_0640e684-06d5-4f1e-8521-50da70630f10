<script setup lang="ts">
defineProps<{
  companies: {
    current_company_name: string
    logo?: string
  }
}>()

// 获取当前时间
// 例如：上午好，下午好，晚上好
// 根据当前时间显示不同的问候语
const greeting = (() => {
  const hour = new Date().getHours()
  if (hour < 6) { return '凌晨好' }
  if (hour < 9) { return '早上好' }
  if (hour < 12) { return '上午好' }
  if (hour < 14) { return '中午好' }
  if (hour < 18) { return '下午好' }
  if (hour < 22) { return '晚上好' }
  return '夜里好'
})()

function goToMy() {
  uni.switchTab({
    url: '/pages/my/index',
  })
}
</script>

<template>
  <view class="header-container">
    <view class="header-content">
      <!-- 左侧标志和名称 -->
      <view class="logo-container">
        <view class="logo-icon size-8 overflow-hidden">
          <img v-if="companies?.current_company_icon" :src="companies?.current_company_icon" alt="Logo" class="size-full object-cover">
        </view>
        <text class="logo-text">{{ companies?.current_company_introduction || '' }}</text>
      </view>

      <!-- 右侧问候语 -->
      <view class="greeting-container" @click="goToMy">
        <text class="greeting-text">{{ greeting }}</text>
        <uni-icons type="contact" size="20" color="#fff" class="ml-1 opacity-80" />
      </view>
    </view>

    <!-- 装饰元素 -->
    <view class="header-decoration">
      <view class="decoration-circle-1" />
      <view class="decoration-circle-2" />
    </view>
  </view>
</template>

<style scoped>
.header-container {
  @apply relative overflow-hidden;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
}

.header-content {
  @apply flex items-center justify-between px-4 py-3 relative z-10;
}

.logo-container {
  @apply flex items-center w-64;
}

.logo-icon {
  @apply flex items-center justify-center rounded-full bg-white/20;
}

.logo-text {
  @apply ml-2 text-base font-medium text-white truncate ;
}

.greeting-container {
  @apply flex items-center;
}

.greeting-text {
  @apply text-sm text-white/90;
}

/* 装饰元素 */
.header-decoration {
  @apply absolute inset-0 overflow-hidden;
  z-index: 1;
}

.decoration-circle-1 {
  @apply absolute rounded-full bg-white opacity-5;
  width: 120px;
  height: 120px;
  top: -60px;
  right: -20px;
}

.decoration-circle-2 {
  @apply absolute rounded-full bg-white opacity-5;
  width: 80px;
  height: 80px;
  bottom: -40px;
  left: 30px;
}
</style>
