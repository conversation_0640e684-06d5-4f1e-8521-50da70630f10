<script setup lang="ts">
import { supabase } from '@/services/supabase'
import { useAuthStore } from '@/stores/auth'
import { ref, watch } from 'vue'

interface Props {
  visible?: boolean
  onSuccessCallback?: () => void
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
  (e: 'skip'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  onSuccessCallback: undefined,
})

const emit = defineEmits<Emits>()

const authStore = useAuthStore()
const { user } = storeToRefs(authStore)
const { checkSession } = authStore

// 弹窗引用
const wechatBindPopup = ref()

// 状态管理
const loading = ref(false)
const errorMessage = ref('')

// 监听 visible 变化，控制弹窗显示
watch(() => props.visible, (newVal) => {
  if (newVal) {
    wechatBindPopup.value?.open()
  }
  else {
    wechatBindPopup.value?.close()
  }
})

// 绑定微信账号
async function bindWechatAccount() {
  loading.value = true
  errorMessage.value = ''

  // 显示加载中提示
  uni.showLoading({
    title: '绑定中...',
    mask: true,
  })

  try {
    // 调用微信登录接口获取临时登录凭证（code）
    const loginResult: any = await new Promise((resolve, reject) => {
      uni.login({
        success: resolve,
        fail: reject,
      })
    })

    if (!loginResult || !loginResult.code) {
      throw new Error('微信登录失败，未获取到授权码')
    }

    // 使用code调用Supabase的微信账号绑定接口
    // @ts-ignore - 微信小程序环境下的特殊方法
    const { data: wxData, error: bindError } = await supabase.auth.wechatBindAccount({ code: loginResult.code })

    if (bindError) {
      if (bindError.status === 422) {
        uni.showModal({
          title: '绑定失败',
          content: '此微信账号已绑定其他用户，请使用其他微信账号',
          showCancel: false,
        })
      }
      else {
        throw bindError
      }
      return
    }

    if (!wxData.user) {
      throw new Error('绑定微信账号失败')
    }

    // 更新用户信息
    authStore.InitupdatePublicUser(wxData.user, true)

    uni.showToast({
      title: '微信账号绑定成功',
      icon: 'success',
      duration: 1000,
    })

    // 关闭弹窗
    closePopup()
    await checkSession()

    // 触发成功事件
    emit('success')

    // 执行成功回调
    if (props.onSuccessCallback) {
      setTimeout(() => {
        props.onSuccessCallback()
      }, 1000)
    }
  }
  catch (error) {
    console.error('绑定微信账号失败:', error)

    uni.showToast({
      title: '绑定失败，请重试',
      icon: 'none',
      duration: 1000,
    })
  }
  finally {
    loading.value = false
    uni.hideLoading()
  }
}

// 跳转到协议页面
function GoToDeclaration(page: string) {
  uni.navigateTo({
    url: `/subdeclaration/declaration/${page}`,
  })
}

// 跳过绑定微信账号
function skipBindWechat() {
  closePopup()
  emit('skip')
}

// 关闭弹窗
function closePopup() {
  wechatBindPopup.value?.close()
  emit('update:visible', false)
}

// 暴露方法给父组件
defineExpose({
  open: () => wechatBindPopup.value?.open(),
  close: closePopup,
})
</script>

<template>
  <uni-popup ref="wechatBindPopup" type="center" :safe-area="true" :mask-click="false">
    <view class="wechat-bind-popup">
      <!-- 头部图标 -->
      <view class="popup-header">
        <view class="title-section">
          <view class="sub-title">
            关联当前微信账号，方便后续登录
          </view>
        </view>
      </view>

      <!-- 描述区域 -->
      <view class="description-section">
        <view class="security-text">
          绑定后可使用微信账号快速登录
        </view>

        <view class="benefits-section">
          <view class="benefits-title">
            绑定微信账号的好处：
          </view>
          <view class="benefit-item">
            <uni-icons type="checkmarkempty" size="16" color="#22C55E" />
            <text class="benefit-text">快速登录，无需输入密码</text>
          </view>
          <view class="benefit-item">
            <uni-icons type="checkmarkempty" size="16" color="#22C55E" />
            <text class="benefit-text">账号安全，微信官方保障</text>
          </view>
          <view class="benefit-item">
            <uni-icons type="checkmarkempty" size="16" color="#22C55E" />
            <text class="benefit-text">多端同步，数据不丢失</text>
          </view>
        </view>
      </view>

      <!-- 绑定表单 -->
      <view class="bind-form-section">
        <!-- 微信绑定按钮 -->
        <button
          class="wechat-bind-btn"
          :disabled="loading"
          @click="bindWechatAccount"
        >
          <view class="btn-content">
            <uni-icons v-if="!loading" type="weixin" size="20" color="#fff" />
            <uni-icons v-else type="spinner-cycle" size="20" color="#fff" />
            <text class="btn-text">{{ loading ? '绑定中...' : '绑定微信账号' }}</text>
          </view>
        </button>

        <!-- 错误提示 -->
        <view v-if="errorMessage" class="error-toast">
          <uni-icons type="info" size="16" color="#EF4444" />
          <text class="error-text">{{ errorMessage }}</text>
        </view>
      </view>

      <!-- 底部跳过按钮 -->
      <view class="skip-section">
        <text class="skip-btn" @click="skipBindWechat">暂不绑定</text>
      </view>
    </view>
  </uni-popup>
</template>

<style scoped>
/* 弹窗容器 */
.wechat-bind-popup {
  @apply relative w-80 rounded-2xl bg-white p-2 shadow-2xl py-6;
  max-height: 90vh;
  overflow-y: auto;
  animation: popupSlideIn 0.3s ease-out;
}

@keyframes popupSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 关闭按钮 */
.close-btn {
  @apply absolute right-4 top-4 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full bg-gray-100 transition-all duration-200;
}

.close-btn:hover {
  @apply bg-gray-200;
}

.close-btn:active {
  @apply bg-gray-300;
  transform: scale(0.95);
}

/* 头部区域 */
.popup-header {
  @apply mb-6 flex flex-col items-center text-center;
}

.welcome-icon {
  @apply mb-4;
}

.icon-bg {
  @apply flex h-16 w-16 items-center justify-center rounded-full bg-green-50;
  box-shadow: 0 4px 12px rgba(7, 193, 96, 0.15);
}

.title-section {
  @apply space-y-1;
}

.main-title {
  @apply flex items-center justify-center space-x-2;
}

.welcome-emoji {
  @apply text-2xl;
}

.title-text {
  @apply text-xl font-bold text-gray-800;
}

.sub-title {
  @apply text-base font-medium text-gray-600;
}

/* 描述区域 */
.description-section {
  @apply mb-6 space-y-4;
}

.security-text {
  @apply text-center text-sm font-medium text-gray-500;
}

.benefits-section {
  @apply rounded-lg bg-gray-50 p-4;
}

.benefits-title {
  @apply mb-3 text-sm font-medium text-gray-700;
}

.benefit-item {
  @apply mb-2 flex items-center space-x-3 last:mb-0;
}

.benefit-text {
  @apply text-sm text-gray-600;
}

/* 绑定表单区域 */
.bind-form-section {
  @apply mb-6 space-y-4;
}

.wechat-bind-btn {
  @apply w-full rounded-lg bg-green-500 py-3 font-medium text-white transition-all duration-200 active:scale-95;
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%);
  box-shadow: 0 4px 12px rgba(7, 193, 96, 0.3);
}

.wechat-bind-btn:disabled {
  @apply opacity-70;
  transform: none !important;
}

.btn-content {
  @apply flex items-center justify-center space-x-2;
}

.btn-text {
  @apply text-base font-medium;
}

/* 错误提示 */
.error-toast {
  @apply flex items-center space-x-2 rounded-lg bg-red-50 p-3;
}

.error-text {
  @apply text-sm text-red-600;
}

/* 协议同意区域 */
.agreement-section {
  @apply space-y-3;
}

.checkbox-container {
  @apply flex cursor-pointer items-center space-x-2;
}

.custom-checkbox {
  @apply flex h-5 w-5 items-center justify-center rounded border-2 border-gray-300 transition-all duration-200;
}

.custom-checkbox.checked {
  @apply border-green-500 bg-green-500;
}

.agreement-text {
  @apply text-sm text-gray-600;
}

.agreement-links {
  @apply flex items-center justify-center space-x-1;
}

.agreement-link {
  @apply cursor-pointer text-sm text-blue-600 underline;
}

.link-separator {
  @apply text-sm text-gray-500;
}

/* 底部跳过按钮 */
.skip-section {
  @apply flex justify-center;
}

.skip-btn {
  @apply cursor-pointer text-sm text-gray-500 underline;
}
</style>
