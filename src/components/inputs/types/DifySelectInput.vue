<script setup lang="ts">
import { useDifyChatStore } from '@/stores/dify-chat'

const props = defineProps<{
  input: {
    variable: string
    label: string
    required: boolean
    options: string[]
    default?: string
    type: 'select'
  }
  layout?: 'horizontal' | 'vertical'
}>()

const difyChatStore = useDifyChatStore()
const { userInputs } = storeToRefs(difyChatStore)

const data = computed({
  get() {
    return userInputs.value[props.input.variable]
  },
  set(value: string) {
    difyChatStore.$patch((state) => {
      state.userInputs[props.input.variable] = value
    })
  },
})

console.log(props.input, 'inputinput')

// Find the index of the current value in options array
const pickerIndex = computed(() => {
  if (!data.value) { return 0 }
  return props.input.options.findIndex(option => option === data.value)
})

// Handle picker change event
function handlePickerChange(e: any) {
  const index = e.detail.value
  data.value = props.input.options[index]
}
</script>

<template>
  <view
    :class="[
      layout === 'horizontal' ? 'grid grid-cols-4 items-center gap-4' : 'flex flex-col gap-2',
    ]"
  >
    <label
      :for="input.variable" :class="[
        layout === 'horizontal' ? 'text-right' : 'mb-1 mt-2 text-left',
      ]"
    >
      {{ input.label }}
    </label>
    <view class="text-sx rounded-lg border border-solid border-gray-300 p-2 text-gray-500">
      <picker :id="input.variable" :value="pickerIndex" :range="input.options" @change="handlePickerChange">
        <view class="flex items-center justify-between">
          <text v-if="data" class="text-black">{{ data }}</text>
          <text v-else class="text-gray-400">{{ input.label }}</text>
          <uni-icons type="arrowdown" size="16" color="#9CA3AF" />
        </view>
      </picker>
    </view>
  </view>
</template>
