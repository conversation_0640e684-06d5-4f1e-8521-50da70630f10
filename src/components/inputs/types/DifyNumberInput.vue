<script setup lang="ts">
import { useDifyChatStore } from '@/stores/dify-chat'

const props = defineProps<{
  input: {
    label: string
    max_length?: number
    required: boolean
    type: 'number'
    variable: string
  }
  layout?: 'horizontal' | 'vertical'
}>()

const difyChatStore = useDifyChatStore()
const { userInputs } = storeToRefs(difyChatStore)

const data = computed({
  get() {
    return userInputs.value[props.input.variable]
  },
  set(value: string) {
    difyChatStore.$patch((state) => {
      state.userInputs[props.input.variable] = value
    })
  },
})
</script>

<template>
  <view
    :class="[
      layout === 'horizontal'
        ? 'grid grid-cols-4 items-center gap-4'
        : 'flex flex-col gap-2',
    ]"
  >
    <label
      :for="input.variable"
      :class="[layout === 'horizontal' ? 'text-right' : 'mb-1 mt-2 text-left']"
    >
      {{ input.label }}
    </label>
    <input
      :id="input.variable" v-model="data" type="number" :placeholder="input.label" class="rounded-md border border-solid border-gray-300 p-2"
      :class="[layout === 'horizontal' ? 'col-span-3' : 'w-auto']"
    >
  </view>
</template>
