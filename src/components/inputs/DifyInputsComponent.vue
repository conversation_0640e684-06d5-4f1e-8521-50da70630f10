<script setup lang="ts">
import { useDifyChatStore } from '@/stores/dify-chat'
import DifyForm from './types/DifyForm.vue'

defineProps({
  title: {
    type: String,
    default: '应用对话框',
  },
})
const difyChatStore = useDifyChatStore()
const { currentApp, isUploading, showUserInputFormCancelBtn }
  = storeToRefs(difyChatStore)
const { startConversationByInputs } = difyChatStore

function handleCancelClick() {
  difyChatStore.showUserInputForm = false
}
</script>

<template>
  <!-- 展示应用名称 -->
  <header class="flex items-center justify-between border-b p-4 dark:border-b-gray-800">
    <h1 class="text-lg font-semibold">
      {{ title }}
    </h1>
    <!-- 内部内容 -->
  </header>

  <!-- <div class="text-center text-xl text-5xl">{{ title }}</div> -->
  <div class="flex h-full flex-col justify-center">
    <Card class="mx-auto w-full max-w-[600px]">
      <CardHeader class="bg-primary/10 rounded-t-lg">
        <div class="flex items-center text-xl">
          {{ currentApp?.icon }}
          {{ currentApp?.name }}
        </div>
      </CardHeader>
      <CardContent class="pt-8">
        <DifyForm />
      </CardContent>
      <CardFooter class="flex justify-end">
        <Button v-if="showUserInputFormCancelBtn" variant="outline" class="mr-2" @click="handleCancelClick">
          取消
        </Button>
        <Button :disabled="isUploading" @click="startConversationByInputs">
          开始对d话
        </Button>
      </CardFooter>
    </Card>
  </div>
</template>
