
import { supabase } from '@/services/supabase'
import { defineStore } from 'pinia'

export const useLLMModelStore = defineStore('llmModel', () => {
  /** 获取模型列表 */
  async function getLLMModelList(page = 1, pageSize = 10) {
    const { data, error } =  await supabase.rpc("llm_model_list", {
      p_page: page,
      p_page_size: pageSize,
    });

    if (error) {
        throw new Error(`Error getting llm_model_list: ${error.message}`);
      }
      return data;
  }

  return {
    getLLMModelList,
  };
});