import { log } from 'node:console'
import { supabase } from '@/services/supabase'
import { Base64 } from 'js-base64'
import { defineStore } from 'pinia'

interface UserState {
  loginStatus: boolean
  user: any | null
  userRoleInfo: any | null
  loading: boolean
  error: string | null
  wxAuth: string | null
  agreed: boolean
  isPhoneLogin: boolean
}

export const useAuthStore = defineStore('userInfo', {
  state: (): UserState => ({
    loginStatus: false,
    user: null,
    userRoleInfo: null,
    loading: false,
    error: null,
    wxAuth: null,
    agreed: false,
    isPhoneLogin: false,
  }),

  actions: {
    async initialize() {
      const storedUser = uni.getStorageSync('user')
      const storedLoginStatus = uni.getStorageSync('loginStatus')
      const userRoleInfo = uni.getStorageSync('userRoleInfo') || {}
      const wxAuth = uni.getStorageSync('wxAuth') || ''

      if (storedUser && storedLoginStatus === true) {
        this.user = JSON.parse(storedUser)
        this.userRoleInfo = JSON.parse(userRoleInfo)
        this.loginStatus = true
        this.wxAuth = wxAuth
      }
      else {
        // await this.checkSession()
      }
    },
    async initLogin() {
      // 调用微信登录接口获取临时登录凭证（code）
      const loginResult: any = await new Promise((resolve, reject) => {
        uni.login({ success: resolve, fail: reject })
      })

      // 使用code调用Supabase的微信登录接口
      // @ts-ignore - 微信小程序环境下的特殊方法
      const { data: wxData, error: wxError } = await supabase.auth.signInWithWechat({ code: loginResult.code })

      if (!wxError) {
        // 如果用户元数据为空，更新用户信息
        if (wxData.user && (!wxData.user.user_metadata || Object.keys(wxData.user.user_metadata).length === 0)) {
          try {
            const { data: userData, error: _updateError } = await supabase.auth.updateUser({
              data: {
                full_name: wxData.user.phone || '微信用户',
              },
            })

            if (userData) {
              uni.setStorageSync('user', JSON.stringify(userData.user))
              this.user = userData.user
            }
            this.updatePublicUser(this.user, true)
          }
          catch (updateErr: any) {
            // 不中断登录流程，只记录错误
            // eslint-disable-next-line no-console
            console.error('更新用户信息失败:', updateErr)
          }
        }

        // @ts-ignore - 微信小程序环境下的特殊方法
        const { data: userRoleInfo, error: userRoleError } = await supabase.rpc('get_current_user_info', { p_uid: wxData.user.id })
        if (userRoleError) {
          throw userRoleError
        }
        this.userRoleInfo = userRoleInfo

        // 登录成功，保存用户信息
        this.loginStatus = true
        this.user = wxData.user
        uni.setStorageSync('user', JSON.stringify(wxData.user))
        uni.setStorageSync('userRoleInfo', JSON.stringify(userRoleInfo))
        uni.setStorageSync('loginStatus', true)
        const arrayBuffer = JSON.stringify(wxData.session)
        uni.setStorageSync('wxAuth', `sb-ctutangg91hkparu99tg-auth-token=base64-${Base64.encodeURL(arrayBuffer)}`)
      }
    },
    async loginWx(res: string) {
      if (this.agreed === false) {
        uni.showToast({
          icon: 'none',
          title: '请先同意用户协议和隐私政策',
          duration: 2000,
        })
        return
      }

      this.loading = true
      this.error = null

      // 显示加载中提示
      uni.showLoading({
        title: '登录中...',
        mask: true,
      })

      try {
        // 调用微信登录接口获取临时登录凭证（code）
        const loginResult: any = await new Promise((resolve, reject) => {
          uni.login({
            success: resolve,
            fail: reject,
          })
        })

        if (!loginResult || !loginResult.code) {
          throw new Error('微信登录失败，未获取到授权码')
        }

        // 使用code调用Supabase的微信登录接口
        // @ts-ignore - 微信小程序环境下的特殊方法
        const { data: wxData, error: wxError } = await supabase.auth.signInWithWechat({ code: loginResult.code })

        if (wxError) {
          throw wxError
        }

        if (!wxData) {
          throw new Error('登录失败，未获取到用户数据')
        }

        // 绑定手机号

        if (!wxError) {
          // @ts-ignore - 微信小程序环境下的特殊方法
          const { data: userRoleInfo, error: userRoleError } = await supabase.rpc('get_current_user_info', { p_uid: wxData.user.id })
          if (userRoleError) {
            throw userRoleError
          }
          this.userRoleInfo = userRoleInfo

          // 登录成功，保存用户信息
          this.loginStatus = true
          this.user = wxData.user
          uni.setStorageSync('user', JSON.stringify(wxData.user))
          uni.setStorageSync('userRoleInfo', JSON.stringify(userRoleInfo))
          uni.setStorageSync('loginStatus', true)
          const arrayBuffer = JSON.stringify(wxData.session)
          uni.setStorageSync('wxAuth', `sb-ctutangg91hkparu99tg-auth-token=base64-${Base64.encodeURL(arrayBuffer)}`)
          // 如果用户元数据为空，更新用户信息
          if (wxData.user && (!wxData.user.user_metadata || Object.keys(wxData.user.user_metadata).length === 0)) {
            try {
              const { data: userData, error: _updateError } = await supabase.auth.updateUser({
                data: {
                  full_name: wxData.user.phone || '微信用户',
                },
              })

              if (userData) {
                uni.setStorageSync('user', JSON.stringify(userData.user))
                this.user = userData.user
              }
              this.updatePublicUser(this.user, true)
            }
            catch (updateErr: any) {
              // 不中断登录流程，只记录错误
              // eslint-disable-next-line no-console
              console.error('更新用户信息失败:', updateErr)
            }
          }

          // 登录成功提示
          uni.showToast({
            title: '登录成功！',
            icon: 'success',
            duration: 1500,
          })

          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
            })
          }, 1500)
        }
        else {
          // 如果没有获取到手机号码，但已经通过微信认证
          this.loginStatus = true
          this.user = wxData.user
          uni.setStorageSync('user', JSON.stringify(wxData.user))
          uni.setStorageSync('loginStatus', 'true')

          uni.showToast({
            title: '登录成功！',
            icon: 'success',
            duration: 1500,
          })

          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
            })
          }, 1500)
        }
      }
      catch (err: any) {
        // 统一错误处理
        this.error = err.message || '登录失败'
        // eslint-disable-next-line no-console
        console.error('微信登录错误:', err)

        uni.showToast({
          title: this.error || '登录失败',
          icon: 'none',
          duration: 2000,
        })
      }
      finally {
        this.loading = false
        uni.hideLoading()
      }
    },

    async loginH5(phone: string, verifyCode: string) {
      if (this.agreed === false) {
        uni.showToast({
          icon: 'none',
          title: '请先同意用户协议和隐私政策',
          duration: 2000,
        })
        return
      }
      if (!phone || !verifyCode) {
        uni.showToast({
          title: '请输入手机号或验证码',
          icon: 'none',
          duration: 2000,
        })
        return
      }
      this.loading = true
      try {
        const { data: userData, error } = await supabase.auth.verifyOtp({
          phone,
          token: verifyCode,
          type: 'sms',
        })
        if (error) {
          uni.showToast({
            title: '验证码无效或过期',
            icon: 'none',
            duration: 2000,
          })
          throw error
        }

        this.user = userData.user
        this.loginStatus = true
        uni.setStorageSync('user', JSON.stringify(userData.user))
        uni.setStorageSync('loginStatus', true)
        const arrayBuffer = JSON.stringify(userData.session)
        uni.setStorageSync('wxAuth', `sb-ctutangg91hkparu99tg-auth-token=base64-${Base64.encodeURL(arrayBuffer)}`)

        const { data: userRoleInfo, error: userRoleError } = await supabase.rpc('get_current_user_info', {
          p_uid: userData?.user?.id,
        })

        if (userRoleError) {
          throw error
        }
        this.userRoleInfo = userRoleInfo

        uni.setStorageSync('userRoleInfo', JSON.stringify(userRoleInfo))
        uni.showToast({
          title: '登录成功！',
          icon: 'success',
          duration: 1500,
        })
        this.isPhoneLogin = true
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
          })
        }, 1500)
      }
      catch (err: any) {
        this.error = err.message
      }
      finally {
        this.loading = false
      }
    },

    async checkUserInfo<T = string | number>(uid: T) {
      const { data: userRoleInfo, error: userRoleError } = await supabase.rpc('get_current_user_info', {
        p_uid: uid,
      })

      if (userRoleError) {
        throw userRoleError
      }
      this.userRoleInfo = userRoleInfo

      uni.setStorageSync('userRoleInfo', JSON.stringify(userRoleInfo))
    },
    async logout() {
      await supabase.auth.signOut()
      this.user = null
      this.loginStatus = false
      this.userRoleInfo = null
      uni.removeStorageSync('user')
      uni.removeStorageSync('loginStatus')
      uni.removeStorageSync('userRoleInfo')
      uni.removeStorageSync('wxAuth')

      uni.showToast({
        icon: 'none',
        title: '登出成功',
        duration: 2000,

      })
    },

    async checkSession() {
      const { data } = await supabase.auth.getSession()
      // eslint-disable-next-line no-console
      console.log(data, 'checkSession-data')

      if (data.session?.user) {
        const { data: userRoleInfo, error } = await supabase.rpc('get_current_user_info', {
          p_uid: data.session.user.id,
        })

        if (error) {
          throw error
        }
        this.userRoleInfo = userRoleInfo
        this.user = data.session.user
        this.loginStatus = true
        uni.removeStorageSync('user')
        uni.removeStorageSync('loginStatus')
        uni.removeStorageSync('userRoleInfo')
        uni.removeStorageSync('wxAuth')

        uni.setStorageSync('user', JSON.stringify(data.session.user))
        uni.setStorageSync('loginStatus', true)
        uni.setStorageSync('userRoleInfo', JSON.stringify(userRoleInfo))
        const arrayBuffer = JSON.stringify(data.session)
        const wxAuth = `sb-ctutangg91hkparu99tg-auth-token=base64-${Base64.encodeURL(arrayBuffer)}`
        uni.setStorageSync('wxAuth', wxAuth)
      }
      else {
        uni.navigateTo({
          url: '/pages/auth/login',
        })
        // const storedUser = uni.getStorageSync('user')
        // const storedLoginStatus = uni.getStorageSync('loginStatus')
        // const userRoleInfo = uni.getStorageSync('userRoleInfo') || {}
        // const wxAuth = uni.getStorageSync('wxAuth') || ''
        // if (storedUser && storedLoginStatus === true) {
        //   this.user = JSON.parse(storedUser)
        //   this.loginStatus = true
        //   this.userRoleInfo = JSON.parse(userRoleInfo)
        //   this.wxAuth = wxAuth
        // }
        // else {
        //   this.user = null
        //   this.loginStatus = false
        //   this.userRoleInfo = null
        // }
      }
    },
    async InitupdatePublicUser(user: any, isLogin: boolean = false) {
      const uid = user.id
      const email = user.email || null
      const wechatUnionId = user.wechat_unionid || null
      const phone = user.phone || null
      const avatar = user.user_metadata?.avatar_url || null
      const nickname = user.phone || null

      // 修改用户信息回调
      const { data, error } = await supabase.rpc('company_create_or_update_user', {
        p_uid: uid,
        p_nickname: nickname,
        p_avatar: avatar,
        p_wechat_union_id: wechatUnionId,
        p_phone: phone,
        p_email: email,
      })
      if (error) {
        throw new Error(`Error creating or updating user: ${error.message}`)
      }
      // 更新最后登录时间
      if (isLogin) {
        const { error } = await supabase.rpc('company_update_last_login_time', {
          p_uid: uid,
        })
      }

      return data
    },
    async updatePublicUser(user: any, isLogin: boolean = false) {
      const uid = user.id
      const email = user.email || null
      const wechatUnionId = user.wechat_unionid || null
      const phone = user.phone || null
      const avatar = user.user_metadata?.avatar_url || null
      const nickname = user.user_metadata?.full_name || null

      // 修改用户信息回调
      const { data, error } = await supabase.rpc('company_create_or_update_user', {
        p_uid: uid,
        p_nickname: nickname,
        p_avatar: avatar,
        p_wechat_union_id: wechatUnionId,
        p_phone: phone,
        p_email: email,
      })
      if (error) {
        throw new Error(`Error creating or updating user: ${error.message}`)
      }
      // 更新最后登录时间
      if (isLogin) {
        const { error } = await supabase.rpc('company_update_last_login_time', {
          p_uid: uid,
        })
      }

      return data
    },
    async refreshSession() {
      await supabase.auth.refreshSession()
    },
    async clearAuth() {
      this.user = null
      this.loginStatus = false
      this.userRoleInfo = null
      uni.removeStorageSync('user')
      uni.removeStorageSync('loginStatus')
      uni.removeStorageSync('userRoleInfo')
      uni.removeStorageSync('wxAuth')
    },
    async getCurrentUserInfo() {
      return {
        uid: this.user,
        current_company_id: this.userRoleInfo[0].current_company_id,

      }
    },
  },
})
