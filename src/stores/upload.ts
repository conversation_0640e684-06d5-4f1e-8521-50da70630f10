
import { supabase } from '@/services/supabase'
import { defineStore } from 'pinia'

export const uploadStore = defineStore('upload', () => {

    const uploadIcon = async (res: any) => {
       
          const file = res.tempFiles[0]
        try {
            const fileExt =  file.path.split('.').pop()
            const fileName = `${Date.now()}.${fileExt}`;
            const filePath = `icon/${fileName}`; // 存储路径，可根据需要修改
            // 上传图标到存储桶
            let fileF ={tempFilePath:file.path}
            const { error: uploadError } = await supabase.storage
                .from("icon") // 存储桶名称，可根据需要修改
                .upload(filePath, fileF);
            if (uploadError) throw uploadError;
            // 获取公共 URL
            const {
                data: { publicUrl },
            } = supabase.storage.from("icon").getPublicUrl(filePath);
            return publicUrl;
        } catch (error: any) {
            return null;
        }
    };


    return {
        uploadIcon

    }
})
