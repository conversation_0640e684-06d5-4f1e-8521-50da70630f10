import type { Database } from '@/types/database.types'
import type { User } from '@/types/user'
import { supabase } from '@/services/supabase'
import { defineStore } from 'pinia'

export const useUsersStore = defineStore('user', () => {
  const client = supabase

  const getCurrentUserInfo = async () => {
    try {
      const { data, error } = await supabase.rpc(
        'get_current_user_info',
        {
          p_uid: (await supabase.auth.getSession()).data.session?.user.id,
        },
      )
      if (error) { throw new Error(`Error get_current_user_info: ${error.message}`) }
      return data
    }
    catch (err) {
      error.value = '获取用户信息失败'
      console.error(err)
    }
  }
  /**
   * 获取当前登录用户的信息
   * @returns 返回用户信息，包含用户基本信息和当前所在公司信息
   * @throws 如果查询过程中出现错误，抛出包含错误信息的Error对象
   */
  async function getCurrentUser(): Promise<User | null> {
    const authUser = (await supabase.auth.getUser()).data.user
    console.log(authUser, 'authUserauthUserauthUser')

    if (!authUser) {
      throw new Error('No auth user found')
    }

    // 调用数据库函数获取用户信息
    const { data, error } = await client.rpc('get_current_user_info', {
      p_uid: authUser.id,
    })

    if (error) {
      throw new Error(`Error getting user info: ${error.message}`)
    }

    return data

    /*
    -- 数据库函数定义
    CREATE OR REPLACE FUNCTION get_current_user_info(p_uid UUID DEFAULT NULL)
    RETURNS json
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    DECLARE
        v_uid UUID;
        v_user_data json;
        v_is_super_admin boolean := false;
        v_is_admin boolean := false;
        v_is_owner boolean := false;
        v_result json;
    BEGIN
        -- 如果没有传入uid，则使用当前认证用户的uid
        v_uid := COALESCE(p_uid, auth.uid());

        -- 获取用户基本信息
        SELECT json_build_object(
            'uid', u.uid,
            'nickname', u.nickname,
            'avatar', u.avatar,
            'phone', u.phone,
            'email', u.email,
            'is_use_memory', u.is_use_memory,
            'current_company_id', u.current_company_id,
            'latest_login_time', u.latest_login_time,
            'created_at', u.created_at
        )
        INTO v_user_data
        FROM "user" u
        WHERE u.uid = v_uid;

        -- 检查是否为超级管理员
        SELECT EXISTS (
            SELECT 1
            FROM group_users gu
            JOIN "groups" g ON g.id = gu.group_id
            WHERE gu.uid = v_uid
            AND g.name = 'superadmin'
        ) INTO v_is_super_admin;

        -- 如果用户有当前公司，检查公司角色
        IF (v_user_data->>'current_company_id') IS NOT NULL THEN
            SELECT
                cu.is_admin,
                cu.is_owner
            INTO
                v_is_admin,
                v_is_owner
            FROM company_user cu
            WHERE cu.uid = v_uid
            AND cu.company_id = (v_user_data->>'current_company_id')::uuid;
        END IF;

        -- 构建最终返回结果
        SELECT jsonb_build_object(
            'is_super_admin', v_is_super_admin,
            'is_admin', COALESCE(v_is_admin, false),
            'is_owner', COALESCE(v_is_owner, false)
        ) || v_user_data::jsonb INTO v_result;

        RETURN v_result;
    END;
    $$;
    */
  }

  /**
   * 创建或更新用户
   * @param uid - 用户唯一id
   * @param nickname - 昵称，可选
   * @param avatar - 头像，可选
   * @param wechatUnionId - 微信唯一ID，可选
   * @param phone - 电话号码，可选
   * @param email - 邮箱，可选
   * @returns 返回创建或更新后的用户记录数据
   * @throws 如果查询或操作过程中出现错误，抛出包含错误信息的Error对象
   */
  async function createOrUpdateUser(
    uid: string,
    nickname?: string,
    avatar?: string,
    wechatUnionId?: string,
    phone?: string,
    email?: string,
  ): Promise<{ user_id: string }> {
    const { data, error } = await client.rpc('company_create_or_update_user', {
      p_uid: uid,
      p_nickname: nickname,
      p_avatar: avatar,
      p_wechat_union_id: wechatUnionId,
      p_phone: phone,
      p_email: email,
    })
    if (error) {
      throw new Error(`Error creating or updating user: ${error.message}`)
    }
    return data
  }

  /**
   * 检查手机号是否已被注册
   * @param phone - 手机号码
   * @returns 返回布尔值，true表示手机号已被注册，false表示未被注册
   * @throws 如果查询过程中出现错误，抛出包含错误信息的Error对象
   */
  async function isPhoneRegistered(phone: string): Promise<boolean> {
    const { data, error } = await client.rpc('check_phone_registered', {
      p_phone: phone,
    })

    if (error) {
      throw new Error(`Error checking phone registration: ${error.message}`)
    }

    return data

    /*
    -- 检查手机号是否已被注册的数据库函数
    CREATE OR REPLACE FUNCTION check_phone_registered(p_phone TEXT)
    RETURNS boolean
    LANGUAGE plpgsql
    AS $$
    DECLARE
        v_exists boolean;
    BEGIN
        -- 检查手机号是否存在
        SELECT EXISTS (
            SELECT 1
            FROM "user"
            WHERE phone = p_phone
            LIMIT 1
        ) INTO v_exists;

        RETURN v_exists;
    END;
    $$;
    */
  }

  /**
   * 切换用户的当前空间
   * @param uid - 用户唯一id
   * @param currentCompanyId - 当前使用空间ID
   * @returns 返回更新后的用户记录数据
   * @throws 如果更新过程中出现错误，抛出包含错误信息的Error对象
   */
  async function switchUserCurrentCompany(
    uid: string,
    currentCompanyId: string,
  ): Promise<void> {
    const { error } = await client.rpc('company_switch_user_current_company', {
      p_uid: uid,
      p_current_company_id: currentCompanyId,
    })
    if (error) {
      throw new Error(`Error switching user current company: ${error.message}`)
    }
  }

  /**
   * 更新最后登录时间
   * @param uid - 用户唯一id
   * @returns 返回更新后的用户记录数据
   * @throws 如果更新过程中出现错误，抛出包含错误信息的Error对象
   */
  async function updateLastLoginTime(uid: string): Promise<void> {
    const { error } = await client.rpc('company_update_last_login_time', {
      p_uid: uid,
    })
    if (error) {
      throw new Error(`Error updating last login time: ${error.message}`)
    }
  }

  /**
   * 更新用户信息
   * @param uid - 用户唯一id
   * @param nickname - 昵称
   * @param avatar - 头像
   * @param wechatUnionId - 微信唯一ID
   * @param phone - 电话号码
   * @param email - 邮箱
   * @returns 返回更新后的用户记录数据
   * @throws 如果更新过程中出现错误，抛出包含错误信息的Error对象
   */
  async function updateUserInfo(
    uid: string,
    nickname: string,
    avatar: string,
    wechatUnionId: string,
    phone: string,
    email: string,
  ): Promise<void> {
    const { error } = await client.rpc('company_update_user_info', {
      p_uid: uid,
      p_nickname: nickname,
      p_avatar: avatar,
      p_wechat_union_id: wechatUnionId,
      p_phone: phone,
      p_email: email,
    })
    if (error) {
      throw new Error(`Error updating user info: ${error.message}`)
    }
  }

  /**
   * 获取用户列表
   * @param page - 当前页码，从 1 开始
   * @param size - 每页显示的记录数量
   * @returns 用户列表数据，包含用户信息（用户名 头像 电话号码 空间列表[空间id，空间名] 最后登录时间 创建时间）
   * @throws 如果查询过程中出现错误，抛出包含错误信息的Error对象
   */
  async function getUserList(page: number, size: number, keyword?: string) {
    const { data, error, count } = await client.rpc('get_user_list_with_companies', {
      p_page: page,
      p_size: size,
      p_keyword: keyword,
    })

    if (error) {
      throw new Error(`Error getting user list: ${error.message}`)
    }

    return { data, count }

    /*
    -- 获取用户列表及其所属公司的数据库函数
    CREATE OR REPLACE FUNCTION get_user_list_with_companies(
        p_page INTEGER,
        p_size INTEGER,
        p_keyword TEXT DEFAULT NULL
    )
    RETURNS json
    LANGUAGE plpgsql
    AS $$
    DECLARE
        v_offset INTEGER;
        v_user_data json;
        v_count INTEGER;
        v_result json;
    BEGIN
        -- 计算偏移量
        v_offset := (p_page - 1) * p_size;

        -- 先获取符合条件的总用户数
        SELECT COUNT(*)
        INTO v_count
        FROM "user" u
        WHERE
            CASE
                WHEN p_keyword IS NOT NULL THEN
                    u.nickname ILIKE '%' || p_keyword || '%' OR
                    u.phone ILIKE '%' || p_keyword || '%' OR
                    u.email ILIKE '%' || p_keyword || '%'
                ELSE true
            END;

        -- 获取分页后的用户数据
        WITH filtered_users AS (
            SELECT
                u.id,
                u.uid,
                u.nickname,
                u.avatar,
                u.phone,
                u.latest_login_time,
                u.created_at
            FROM "user" u
            WHERE
                CASE
                    WHEN p_keyword IS NOT NULL THEN
                        u.nickname ILIKE '%' || p_keyword || '%' OR
                        u.phone ILIKE '%' || p_keyword || '%' OR
                        u.email ILIKE '%' || p_keyword || '%'
                    ELSE true
                END
            ORDER BY u.created_at DESC
            LIMIT p_size
            OFFSET v_offset
        ),
        user_companies AS (
            SELECT
                cu.uid,
                json_agg(
                    json_build_object(
                        'id', c.id,
                        'name', c.name
                    )
                ) as companies
            FROM company_user cu
            JOIN companies c ON c.id = cu.company_id
            GROUP BY cu.uid
        )
        SELECT
            json_agg(
                json_build_object(
                    'id', fu.id,
                    'uid', fu.uid,
                    'nickname', fu.nickname,
                    'avatar', fu.avatar,
                    'phone', fu.phone,
                    'latest_login_time', fu.latest_login_time,
                    'created_at', fu.created_at,
                    'companies', COALESCE(uc.companies, '[]'::json)
                )
            )
        INTO v_user_data
        FROM filtered_users fu
        LEFT JOIN user_companies uc ON uc.uid = fu.uid;

        -- 构建最终返回结果
        SELECT json_build_object(
            'data', COALESCE(v_user_data, '[]'::json),
            'count', v_count
        ) INTO v_result;

        RETURN v_result;
    END;
    $$;
    */
  }

  /**
   * 获取公司用户列表
   * @param page - 当前页码，从 1 开始
   * @param size - 每页显示的记录数量
   * @returns 用户列表数据，包含用户信息（用户名 头像 电话号码 空间列表[空间id，空间名] 最后登录时间 创建时间）
   * @throws 如果查询过程中出现错误，抛出包含错误信息的Error对象
   */
  async function getCompanyUserList(
    page: number,
    size: number,
    companyId: string,
    keyword?: string,
  ) {
    const { data, error, count } = await client.rpc('get_company_user_list', {
      p_company_id: companyId,
      p_page: page,
      p_size: size,
      p_keyword: keyword,
    })

    if (error) {
      throw new Error(`Error getting company user list: ${error.message}`)
    }

    return { data, count }

    /*
    -- 获取公司用户列表的数据库函数
    CREATE OR REPLACE FUNCTION get_company_user_list(
        p_company_id UUID,
        p_page INTEGER,
        p_size INTEGER,
        p_keyword TEXT DEFAULT NULL
    )
    RETURNS json
    LANGUAGE plpgsql
    AS $$
    DECLARE
        v_offset INTEGER;
        v_user_data json;
        v_count INTEGER;
        v_result json;
    BEGIN
        -- 计算偏移量
        v_offset := (p_page - 1) * p_size;

        -- 先获取总数
        SELECT COUNT(*)
        INTO v_count
        FROM company_user cu
        JOIN "user" u ON u.uid = cu.uid
        WHERE cu.company_id = p_company_id
        AND (
            CASE
                WHEN p_keyword IS NOT NULL THEN
                    u.nickname ILIKE '%' || p_keyword || '%' OR
                    u.phone ILIKE '%' || p_keyword || '%'
                ELSE true
            END
        );

        -- 获取分页后的公司用户数据
        WITH company_user AS (
            SELECT
                cu.id,
                cu.uid,
                cu.is_admin,
                cu.is_owner,
                cu.last_use_time,
                c.id as company_id,
                c.name as company_name,
                a.id as app_id,
                a.name as app_name,
                u.nickname,
                u.avatar,
                u.phone,
                u.latest_login_time,
                u.created_at
            FROM company_user cu
            JOIN companies c ON c.id = cu.company_id
            LEFT JOIN apps a ON a.id = cu.latest_use_app_id
            JOIN "user" u ON u.uid = cu.uid
            WHERE cu.company_id = p_company_id
            AND (
                CASE
                    WHEN p_keyword IS NOT NULL THEN
                        u.nickname ILIKE '%' || p_keyword || '%' OR
                        u.phone ILIKE '%' || p_keyword || '%'
                    ELSE true
                END
            )
            ORDER BY u.created_at DESC
            LIMIT p_size
            OFFSET v_offset
        )
        SELECT
            json_agg(
                json_build_object(
                    'id', cu.id,
                    'uid', cu.uid,
                    'company_id', cu.company_id,
                    'company_name', cu.company_name,
                    'latest_use_app_name', cu.app_name,
                    'nickname', cu.nickname,
                    'avatar', cu.avatar,
                    'phone', cu.phone,
                    'company_user_role',
                        CASE
                            WHEN cu.is_owner THEN 'companyowner'
                            WHEN cu.is_admin THEN 'companyadmin'
                            ELSE 'user'
                        END,
                    'last_use_time', cu.last_use_time,
                    'latest_login_time', cu.latest_login_time,
                    'created_at', cu.created_at
                )
            )
        INTO v_user_data
        FROM company_user cu;

        -- 构建最终返回结果
        SELECT json_build_object(
            'data', COALESCE(v_user_data, '[]'::json),
            'count', v_count
        ) INTO v_result;

        RETURN v_result;
    END;
    $$;
    */
  }

  /**
   * 获取用户的公司列表
   * @returns 返回一个对象，包含是否为平台管理员的布尔值以及公司列表数据，公司列表包含公司ID，公司名，用户所在公司角色company_user_role 可选项 "companyowner"| "companyadmin"|"user"
   * @throws 如果查询过程中出现错误，抛出包含错误信息的Error对象
   */
  async function getUserCompanyList() {
    const authUser = (await supabase.auth.getUser()).data.user
    if (!authUser) {
      throw new Error('No auth user found')
    }
    const { data, error } = await client.rpc('get_user_company_list', {
      p_uid: authUser.id,
    })

    if (error) {
      throw new Error(`Error getting user company list: ${error.message}`)
    }

    return data

    /*
    -- 获取用户公司列表的数据库函数
    CREATE OR REPLACE FUNCTION get_user_company_list(p_uid UUID)
    RETURNS json
    LANGUAGE plpgsql
    AS $$
    DECLARE
        v_is_super_admin boolean;
        v_company_list json;
        v_result json;
    BEGIN
        -- 检查是否为超级管理员
        SELECT EXISTS (
            SELECT 1
            FROM group_users gu
            JOIN "groups" g ON g.id = gu.group_id
            WHERE gu.uid = p_uid
            AND g.name = 'superadmin'
        ) INTO v_is_super_admin;

        -- 获取用户的公司列表
        SELECT json_agg(
            json_build_object(
                'company_id', c.id,
                'company_name', c.name,
                'company_user_role',
                    CASE
                        WHEN cu.is_owner THEN 'companyowner'
                        WHEN cu.is_admin THEN 'companyadmin'
                        ELSE 'user'
                    END
            )
        )
        INTO v_company_list
        FROM company_user cu
        JOIN companies c ON c.id = cu.company_id
        WHERE cu.uid = p_uid;

        -- 构建最终返回结果
        SELECT json_build_object(
            'isSuperAdmin', v_is_super_admin,
            'companyList', COALESCE(v_company_list, '[]'::json)
        ) INTO v_result;

        RETURN v_result;
    END;
    $$;
    */
  }

  /**
   * 设置公司用户为管理员
   * @param uid - 用户唯一id
   * @param companyId - 公司id
   * @returns 返回更新后的公司用户记录数据
   * @throws 如果更新过程中出现错误，抛出包含错误信息的Error对象
   */
  async function setCompanyUserAsAdmin(
    uid: string,
    companyId: string,
  ): Promise<void> {
    const { error } = await client.rpc('company_set_company_user_as_admin', {
      p_uid: uid,
      p_company_id: companyId,
    })
    if (error) {
      throw new Error(`Error setting company user as admin: ${error.message}`)
    }
  }

  /**
   * 移除公司管理员身份
   * @param uid - 用户唯一id
   * @param companyId - 公司id
   * @returns 返回更新后的公司用户记录数据
   * @throws 如果更新过程中出现错误，抛出包含错误信息的Error对象
   */
  async function removeCompanyAdminStatus(
    uid: string,
    companyId: string,
  ): Promise<void> {
    const { error } = await client.rpc('company_remove_company_admin_status', {
      p_uid: uid,
      p_company_id: companyId,
    })
    if (error) {
      throw new Error(`Error removing company admin status: ${error.message}`)
    }
  }

  /**
   * 将用户从指定空间中移除
   * @param uid - 用户唯一id
   * @param companyId - 公司id
   * @returns 返回删除操作的结果
   * @throws 如果删除过程中出现错误，抛出包含错误信息的Error对象
   */
  async function removeUserFromCompany(
    uid: string,
    companyId: string,
  ): Promise<void> {
    const { error } = await client.rpc('company_remove_user_from_company', {
      p_uid: uid,
      p_company_id: companyId,
    })
    if (error) {
      throw new Error(`Error removing user from company: ${error.message}`)
    }
  }

  // 返回当前角色 user||superadmin||spaceowner||spaceadmin  =》 普通用户  超级管理员  空间所有者  空间管理员
  async function getRole() {
    const user = await getCurrentUser()
    if (!user) {
      return 'user'
    }

    if (user.is_super_admin) {
      return 'superadmin'
    }

    if (user.is_owner) {
      return 'companyowner'
    }

    if (user.is_admin) {
      return 'companyadmin'
    }

    return 'user'
  }

  /**
   * 更新用户最近使用的应用ID
   * @param uid - 用户唯一id
   * @param companyId - 公司id
   * @param latestUseAppId - 最近使用的应用ID
   * @returns void
   * @throws 如果更新过程中出现错误，抛出包含错误信息的Error对象
   */
  async function updateLatestUseAppId(
    companyId: string,
    uid: string,
    latestUseAppId: string,
  ): Promise<void> {
    const { error } = await client.rpc('user_update_latest_use_app_id', {
      p_company_id: companyId,
      p_uid: uid,
      p_latest_use_app_id: latestUseAppId,
    })
    if (error) {
      throw new Error(`Error updating latest use app id: ${error.message}`)
    }
  }

  async function getUserToken(userId: string) {
    const { data, error } = await client.rpc('get_user_token_info', {
      p_uid: userId,
    })

    if (error) {
      throw new Error(`Error getting user token info: ${error.message}`)
    }

    return data

    /*
    -- 获取用户积分信息的数据库函数
    CREATE OR REPLACE FUNCTION get_user_token_info(p_uid UUID)
    RETURNS json
    LANGUAGE plpgsql
    AS $$
    DECLARE
        v_total_points INTEGER;
        v_consumed_points INTEGER;
        v_result json;
    BEGIN
        -- 获取用户积分信息
        SELECT
            COALESCE(total_points, 0),
            COALESCE(consumed_points, 0)
        INTO
            v_total_points,
            v_consumed_points
        FROM user_amount_management
        WHERE uid = p_uid;

        -- 如果用户没有积分记录，返回默认值
        IF v_total_points IS NULL THEN
            SELECT json_build_object(
                'total_points', 0,
                'consumed_points', 0,
                'available_points', 0
            ) INTO v_result;
        ELSE
            -- 构建返回结果
            SELECT json_build_object(
                'total_points', v_total_points,
                'consumed_points', v_consumed_points,
                'available_points', v_total_points - v_consumed_points
            ) INTO v_result;
        END IF;

        RETURN v_result;
    END;
    $$;
    */
  }

  return {
    getCurrentUserInfo,
    getCurrentUser,
    createOrUpdateUser,
    isPhoneRegistered,
    switchUserCurrentCompany,
    updateLastLoginTime,
    updateUserInfo,
    getUserList,
    getCompanyUserList,
    getUserCompanyList,
    setCompanyUserAsAdmin,
    removeCompanyAdminStatus,
    removeUserFromCompany,
    getRole,
    updateLatestUseAppId,
    getUserToken,
  }
})
