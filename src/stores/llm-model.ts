import { supabase } from '@/services/supabase'
import { defineStore } from 'pinia'

export const useLLMModelStore = defineStore('llmModel', () => {
  const client = supabase

  // --------------------
  // 📝 写操作（调用数据库函数）
  // --------------------
  /** 创建 LLM 模型 */
  async function createLLMModel(
    modelName: string, // 模型名称
    modelProvider: string, // 模型供应商
    promptPremiumRate: number, // 提示词溢价率
    completionPremiumRate: number, // 补全溢价率
  ) {
    const { data, error } = await client.rpc('llm_model_create', {
      p_model_name: modelName,
      p_model_provider: modelProvider,
      p_prompt_premium_rate: promptPremiumRate,
      p_completion_premium_rate: completionPremiumRate,
    })

    if (error) { throw new Error(`创建失败: ${error.message}`) }
    return data // 只返回 ID
  }

  /** 更新 LLM 模型 */
  async function updateLLMModel(
    id: string,
    modelName: string,
    modelProvider: string,
    promptPremiumRate: number,
    completionPremiumRate: number,
  ) {
    const { error } = await client.rpc('llm_model_update', {
      p_id: id,
      p_model_name: modelName,
      p_model_provider: modelProvider,
      p_prompt_premium_rate: promptPremiumRate,
      p_completion_premium_rate: completionPremiumRate,
    })

    if (error) { throw new Error(`更新失败: ${error.message}`) }
  }

  /** 删除 LLM 模型 */
  async function deleteLLMModel(id: string) {
    const { error } = await client.rpc('llm_model_delete', { p_id: id })
    if (error) { throw new Error(`删除失败: ${error.message}`) }
  }
  // --------------------
  // 🔍 读操作（直接使用 Supabase 查询）
  // --------------------

  /** 获取单个模型 */
  async function getLLMModelDetail(id: string) {
    const { data, error } = await client.rpc('llm_model_detail', {
      p_id: id,
    })

    if (error) { throw new Error(`查询失败: ${error.message}`) }
    if (!data) { throw new Error('模型不存在') }
    return data

    /*
-- 获取单个 LLM 模型详情的数据库函数
CREATE OR REPLACE FUNCTION llm_model_detail(
  p_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_result JSON;
BEGIN
  -- 获取模型详情
  SELECT json_build_object(
    'id', m.id,
    'model_name', m.model_name,
    'model_provider', m.model_provider,
    'completion_premium_rate', m.completion_premium_rate,
    'prompt_premium_rate', m.prompt_premium_rate,
    'created_at', m.created_at,
    'updated_at', m.updated_at
  )
  INTO v_result
  FROM llm_model m
  WHERE m.id = p_id;

  IF v_result IS NULL THEN
    RETURN NULL;
  END IF;

  RETURN v_result;
END;
$$;
*/
  }

  /** 获取模型列表 */
  async function getLLMModelList(page = 1, pageSize = 10) {
    const { data, error } = await client.rpc('llm_model_list', {
      p_page: page,
      p_page_size: pageSize,
    })

    if (error) { throw new Error(`列表查询失败: ${error.message}`) }
    return data

    /*
-- 获取 LLM 模型列表的数据库函数
CREATE OR REPLACE FUNCTION llm_model_list(
  p_page INTEGER,
  p_page_size INTEGER
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_start INTEGER;
  v_end INTEGER;
  v_total INTEGER;
  v_result JSON;
BEGIN
  -- 计算分页起始位置
  v_start := (p_page - 1) * p_page_size;

  -- 获取总记录数
  SELECT COUNT(*) INTO v_total FROM llm_model;

  -- 构建返回结果
  SELECT json_build_object(
    'list', COALESCE(
      (SELECT json_agg(t.*)
       FROM (
         SELECT *
         FROM llm_model
         ORDER BY created_at DESC
         LIMIT p_page_size
         OFFSET v_start
       ) t
      ), '[]'::json
    ),
    'total', v_total
  ) INTO v_result;

  RETURN v_result;
END;
$$;
*/
  }

  /** 根据模型名称和供应商获取模型 */
  async function getLLMModelByNameAndProvider(
    modelName: string,
    modelProvider: string,
  ) {
    const { data, error } = await client.rpc('llm_model_get_by_name_provider', {
      p_model_name: modelName,
      p_model_provider: modelProvider,
    })

    if (error) { throw new Error(`查询失败: ${error.message}`) }
    return data
  }

  return {
    createLLMModel,//TODO:暂无使用
    updateLLMModel,//TODO:暂无使用
    deleteLLMModel,//TODO:暂无使用
    getLLMModelDetail,//TODO:暂无使用
    getLLMModelList,//TODO:暂无使用
    getLLMModelByNameAndProvider,
  }
})
