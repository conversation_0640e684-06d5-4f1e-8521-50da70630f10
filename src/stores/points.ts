import { supabase } from '@/services/supabase'
import { defineStore } from 'pinia'

export const usePointsStore = defineStore('userPoints', () => {
  const client = supabase

  /**
   * 充值并兑换积分
   * @param companyId - 公司ID
   * @param transactionTime - 交易时间
   * @param amount - 充值金额
   * @param remark - 备注信息
   * @returns 返回包含所有者ID和流水记录ID的对象
   * @throws 如果在任何步骤中出现错误，抛出包含错误信息的 Error 对象
   */
  async function rechargeAndExchange(
    companyId: string,
    transactionTime: Date,
    amount: number,
    points: number,
    remark: string,
  ) {
    const { data, error } = await client.rpc('points_recharge_and_exchange', {
      p_company_id: companyId,
      p_transaction_time: transactionTime,
      p_amount: amount,
      p_points: points,
      p_remark: remark,
    })
    if (error) {
      throw new Error(
        `${error.message}`,
      )
    }
    return data
  }

  /**
   * 获取兑换值列表
   * @param page - 页码，从1开始
   * @param size - 每页条数，默认10
   * @returns 返回兑换记录列表和总记录数
   * 返回的数据结构
   * @typedef {object} ExchangeRecord
   * @property {string} company_name - 空间名称
   * @property {number} transfer_amount - 转账金额
   * @property {number} exchange_power_value - 兑换的电力值
   * @property {string} transfer_time - 转账时间
   * @property {string} create_time - 创建时间
   * @property {string} remark - 备注信息
   */

  async function getExchangeRecords(
    page: number = 1,
    size: number = 10,
    company_id?: string,
  ) {
    const { data, error } = await client.rpc('points_get_exchange_records', {
      p_page: page,
      p_size: size,
      p_company_id: company_id,
    })

    if (error) {
      throw new Error(`${error.message}`)
    }

    return data
  }

  /**
   * 通过 company_id 查询空间剩余积分
   * @param companyId - 公司ID
   * @returns 返回包含公司ID和剩余积分的 JSON 对象
   * @throws 如果在任何步骤中出现错误，抛出包含错误信息的 Error 对象
   */
  async function getRemainingPoints(companyId: string) {
    const { data, error } = await client.rpc('get_remaining_points', {
      p_company_id: companyId,
    })

    if (error) {
      throw new Error(`${error.message}`)
    }

    return data
  }

  return {
    rechargeAndExchange,
    getExchangeRecords,
    getRemainingPoints,
  }
})
