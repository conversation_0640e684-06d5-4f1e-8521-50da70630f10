import { UUID } from 'node:crypto'
import { supabase } from '@/services/supabase'
import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', () => {
  const client = supabase

  // 1. 分页获取应用列表（无需修改，保留原逻辑）
  async function getApps(page: number, size: number, keyword?: string) {
    const { data, error } = await client.rpc('get_app_list', {
      p_page: page,
      p_size: size,
      p_keyword: keyword || null,
    })

    if (error) { throw new Error(`${error.message}`) }
    return data
  }

  async function getApp(id: string): Promise<DifyAppConfig> {
    const { data, error } = await client.rpc('get_app_by_id', {
      p_id: id,
    })

    if (error) { throw new Error(`${error.message}`) }
    return data
  }

  async function getCurrentCompanyApps(page: number, size: number, is_listed?: boolean, keyword?: string, startTime?: Date, endTime?: Date) {
    // const user = (await supabase.auth.getUser()).data.user

    // 调用数据库函数获取当前公司的应用列表
    let { data, error } = await client.rpc('get_current_company_apps', {
      p_uid: null,
      p_page: page,
      p_size: size,
      p_is_listed: is_listed || null,
      p_keyword: keyword || null,
      p_start_time: startTime || null,
      p_end_time: endTime || null,
    })

    if (error) { throw new Error(`Error getting company apps: ${error.message}`) }
    if (data.length === 0) {
      return {
        count: 0,
        data: [],
      }
    }
    data = data[0]

    // 遍历data数组，为每个应用添加model_type_name字段
    data.data.forEach((item: any) => {
      item.model_type_name = getAppTypeByModelType(item.model_type)
    })

    return data
  }

  function getAppTypeByModelType(modelType: ModelType): AppType {
    switch (modelType) {
      case 'chat':
        return '聊天助手'
      case 'workflow':
        return '工作流'
      case 'completion':
        return '文本助手'
      default:
        throw new Error(`Invalid model type: ${modelType}`)
    }
  }

  type AppType = '聊天助手' | '工作流' | '文本助手'

  const MODEL_TYPES = ['chat', 'workflow', 'completion'] as const
  type ModelType = typeof MODEL_TYPES[number]

  // 获取所有 ModelType 可选项
  function getAllModelTypes(): ModelType[] {
    return [...MODEL_TYPES]
  }

  // 2. 创建应用（调用数据库函数）
  async function createApp(
    companyId: string, // 必传
    name: string, // 必传
    icon: string, // 必传
    description: string, // 必传
    tags: string[],
    apikey: string, // 必传
    modelType: string, // 必传
    categoryId: string, // 必传
    companyUserPointsRate: number, // 值必须大于等于0
  ) {
    const { data, error } = await client.rpc('app_create', {
      p_company_id: companyId,
      p_name: name,
      p_icon: icon,
      p_description: description,
      p_tags: tags,
      p_apikey: apikey,
      p_model_type: modelType,
      p_category_id: categoryId,
      p_company_user_points_rate: companyUserPointsRate,
      p_opening_statement: '',
    })
    console.log('获取的参数', data)
    if (error) { throw new Error(`Error creating app: ${error.message}`) }
    return data
  }

  // 3. 更新应用（调用数据库函数）
  async function updateApp(
    id: string, // 必传
    name: string, // 必传
    icon: string, // 必传
    description: string, // 必传
    tags: string[],
    apikey: string, // 必传
    modelType: string, // 必传
    categoryId: string, // 必传
    companyUserPointsRate: number, // 值必须大于等于0
  ) {
    const { error } = await client.rpc('app_update', {
      p_id: id,
      p_name: name,
      p_icon: icon,
      p_description: description,
      p_tags: tags,
      p_apikey: apikey,
      p_model_type: modelType,
      p_category_id: categoryId,
      p_company_user_points_rate: companyUserPointsRate,
      p_opening_statement: '',
    })
    if (error) { throw new Error(`Error updating app: ${error.message}`) }
    return id
  }

  // 4. 删除应用（调用数据库函数）
  async function deleteApp(id: string) {
    const { error } = await client.rpc('app_delete', { p_id: id })
    if (error) { throw new Error(`Error deleting app: ${error.message}`) }
  }

  // 5.更新空间应用信息 (使用数据库函数)
  async function updateCompanyApp(
    companyId: string, // 必传
    appId: string, // 必传
    isFree?: boolean, // 可选
    pictureRemark?: string, //  可选
    newUserTryTime?: number, // 可选
  ) {
    console.log('sdfsdfdsf', companyId, appId, isFree, pictureRemark, newUserTryTime)
    const { error } = await client.rpc('company_app_update', {
      p_company_id: companyId,
      p_app_id: appId,
      p_is_free: isFree,
      p_picture_remark: pictureRemark,
      p_new_user_try_time: newUserTryTime,
    })
    console.log('更新空间信息而荣荣', error)
    if (error) { throw new Error(`Error updating company app: ${error.message}`) }
  }

  // 5. 获取应用套餐列表
  async function getCompanyAppConfig(companyId: string, appId: string) {
    const { data, error } = await client.rpc('get_company_app_config', {
      p_company_id: companyId,
      p_app_id: appId,
    })

    if (error) { throw new Error(`Error getting company app config: ${error.message}`) }
    return data
  }

  // 5. 上下架应用（调用数据库函数）
  async function toggleAppListing(id: string, isListed: boolean) {
    const { error } = await client.rpc('app_toggle_listing', {
      p_id: id,
      p_is_listed: isListed,
    })

    if (error) { throw new Error(`Error toggling app listing: ${error.message}`) }
  }

  // 6. 添加应用套餐（调用数据库函数）
  async function addAppPackage(
    companyId: string,
    appId: string,
    packageType: PackageType,
    name: string,
    pointsPerTime?: number,
    pointsPerDay?: number,
    maxUsagePerDay?: number,
    times?: number,
    days?: number,
  ) {
    const { data, error } = await client.rpc('app_add_company_app_config', {
      p_company_id: companyId,
      p_app_id: appId,
      p_package_type: packageType,
      p_name: name,
      p_points_per_time: pointsPerTime,
      p_points_per_day: pointsPerDay,
      p_max_usage_per_day: maxUsagePerDay,
      p_times: times,
      p_days: days,
    })

    if (error) { throw new Error(`Error adding app package: ${error.message}`) }
    return data
  }

  // 删除应用套餐
  async function deleteAppPackage(id: string) {
    const { error } = await client.rpc('app_delete_company_app_config', {
      p_id: id,
    })

    if (error) { throw new Error(`Error deleting app package: ${error.message}`) }
  }

  // 5. 获取应用套餐列表
  async function getCompanyAppConfigList(companyId?: string, appId?: string | null, page?: number, size?: number) {
    const { data, error } = await client.rpc('get_company_app_config_list', {
      p_company_id: companyId,
      p_app_id: appId,
      p_page: page,
      p_size: size,
    })

    if (error) { throw new Error(`Error getting company app config: ${error.message}`) }
    return data
  }

  // 5. 生成兑换码
  async function pointsCreateRedemptionCodes(uid: string, configId: string, days: number, channel: string, count: number) {
    const { data, error } = await client.rpc('points_create_redemption_codes', {
      p_uid: uid,
      p_company_app_config_id: configId,
      p_days: days,
      p_channel: channel,
      p_count: count,
    })

    if (error) { throw new Error(`Error getting company app config: ${error.message}`) }
    return data
  }
  // 5. 获取公司统计信息（累计收入  生成总数 已兑换数量）
  async function pointsGetCompanyRedemptionCodeStatistics(companyId: string) {
    const { data, error } = await client.rpc('points_get_company_redemption_code_statistics', {
      p_company_id: companyId,
    })

    if (error) { throw new Error(`Error getting company app config: ${error.message}`) }
    return data
  }
  // 根据空间-获取兑换码列表
  async function getRedemptioCodes(company_id: string, page: number, size: number, app_id?: string, keyword?: string, is_used?: boolean, config_id?: string) {
    const { data, error } = await client.rpc('get_redemption_codes', {
      p_company_id: company_id,
      p_app_id: app_id,
      p_page: page,
      p_size: size,
      p_keyword: keyword,
      p_company_app_config_id: config_id,
      p_is_used: is_used,
    })

    if (error) {
      throw error
    }

    return data
  };

  // 使用兑换码
  async function useRedemptionCode(userId: string, appid: string, code: string) {
    // 调用存储过程使用兑换码
    return await client.rpc('points_use_redemption_code', {
      p_uid: userId,
      p_app_id: appid,
      p_code: code,
    })
  };

  // 删除兑换码
  async function delRedemptionCode(code: string) {
    // 调用存储过程使用兑换码
    return await client.rpc('points_delete_redemption_code', {
      p_code_id: code,
    })
  };

  async function getQrcode(code: string) {
    const { data, error } = await supabase.auth.getUnlimitedQRCode({
      page: 'pages/workspace/join', // pageUrl，不能带参数，不能带最开始的 /
      scene: code, // 页面参数，字符串最大长度32字节，字符约束参考微信官方文档 41
      check_path: true,
      env_version: 'release',
      width: 430,
      auto_color: false,
      line_color: { r: 0, g: 0, b: 0 },
      is_hyaline: false,
    })
    return data.imgBase64
  }

  // 根据应用ID获取应用详情
  async function getAppById(appId: string) {
    const { data, error } = await client.rpc('get_app_by_id', {
      p_id: appId,
    })

    if (error) {
      throw error
    }

    return data
  }

  async function getCompanyAppByPriceList(appId: string, companyId: string) {
    const { data, error } = await client.rpc('get_company_app_config_list', {
      p_company_id: companyId,
      p_app_id: appId,
    })

    if (error) {
      throw error
    }

    return data
  }

  return {
    getApps,
    getApp,
    getAppById,
    getCurrentCompanyApps,
    createApp,
    updateApp,
    deleteApp,
    updateCompanyApp,
    getCompanyAppConfig,
    toggleAppListing,
    addAppPackage,
    deleteAppPackage,
    delRedemptionCode,
    getCompanyAppConfigList,
    pointsCreateRedemptionCodes,
    pointsGetCompanyRedemptionCodeStatistics,
    getRedemptioCodes,
    useRedemptionCode,
    getQrcode,
    getCompanyAppByPriceList,
  }
})
