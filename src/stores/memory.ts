import { supabase } from '@/services/supabase'
import { defineStore } from 'pinia'

export const useMemoryStore = defineStore('memory', () => {
  const error = ref<string | null>(null)
  const MemoryList = ref([])

  // 是否使用记忆-提交
  const companyUpdateUserMemorySetting = async (is_use_memory: boolean) => {
    try {
      const { error } = await supabase.rpc(
        'company_update_user_memory_setting',
        {
          p_is_use_memory: is_use_memory,
        },
      )
      if (error) { throw new Error(`Error company_update_user_memory_setting: ${error.message}`) }
    }
    catch (err) {
      error.value = '设置是否使用记忆失败'
      console.error(err)
    }
  }

  // 获取记忆列表
  const getUserMemoryList = async (page: number = 1, size: number = 100) => {
    try {
      const { data, error } = await supabase.rpc(
        'get_user_memory_list',
        {
          page,
          size,
        },
      )
      if (error) { throw new Error(`Error get_user_memory_list: ${error.message}`) }
      MemoryList.value = data
      //    console.log("ddddfdfadfasdf",data)
      return data
    }
    catch (err) {
      error.value = '获取记忆列表失败'
      console.error(err)
    }
  }

  // 删除记忆
  const deletUserMemory = async (memory_id: string) => {
    try {
      const { data, error } = await supabase.rpc(
        'delete_user_memory',
        {
          p_id: memory_id,
        },
      )
      if (error) { throw new Error(`Error delete_user_memory: ${error.message}`) }
    }
    catch (err) {
      error.value = '删除记忆失败'
      console.error(err)
    }
  }

  // 编辑跟新增记忆
  const upsertUserMemory = async (key: string, value: string, flag: string, uid: string, is_enable: boolean, id: string) => {
    console.log('upsertUserMemory接收到的值')
    console.log(key, value, flag, uid, is_enable, id)
    console.log('结束')
    try {
      const { error } = await supabase.rpc(
        'upsert_user_memory',
        {
          p_key: key,
          p_value: value,
          p_flag: flag,
          p_uid: uid,
          p_is_enable: is_enable,
          p_id: id,
        },
      )
      if (error) { throw new Error(`Error update_user_memory: ${error.message}`) }
    }
    catch (err) {
      error.value = '操作记忆'
      console.error(err)
    }
  }

  return {
    MemoryList,
    getUserMemoryList,
    deletUserMemory,
    companyUpdateUserMemorySetting,
    upsertUserMemory,

  }
})
