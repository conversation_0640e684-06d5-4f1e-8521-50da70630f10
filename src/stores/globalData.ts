import { supabase } from '@/services/supabase'
import { defineStore } from 'pinia'

export const globalDataStore = defineStore('globalData', {
  state: () => ({
    globalData: {},
    Platform: '',
    deviceInfo: {},
    systemInfo: {
      navBarHeight: 0 as number | undefined,
      menuRight: 0 as number | undefined,
      menuLeft: 0 as number | undefined,
      menuTop: 0 as number | undefined,
      menuHeight: 0 as number | undefined,
    },
  }),

  actions: {
    async initializeSystemInfo() {
      // 胶囊按钮位置信息

      const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
      // 导航栏高度 = 状态栏高度 + 44

      this.systemInfo.menuTop = menuButtonInfo.top
      this.systemInfo.menuHeight = menuButtonInfo.height

      this.deviceInfo = uni.getDeviceInfo()
      console.log(this.deviceInfo, 'deviceInfodeviceInfodeviceInfo')

      const wInfo = uni.getWindowInfo()
      this.systemInfo.navBarHeight = wInfo.statusBarHeight
      this.systemInfo.menuRight = wInfo.screenWidth - menuButtonInfo.right
      this.systemInfo.menuLeft = wInfo.screenWidth - menuButtonInfo.left
    },
    checkPlatform() {
      this.Platform = uni.getAppBaseInfo().uniPlatform
    },
  },
})
