<script setup lang="ts">
import Support from '@/components/common/Support.vue'
import AgentCard from '@/components/index/agentCard.vue'
import AppHeader from '@/components/index/appHeader.vue'
// import BalanceAlert from '@/components/index/balanceAlert.vue'
import { useAgentStore } from '@/stores/agent'
import { useAppStore } from '@/stores/app'
import { useAuthStore } from '@/stores/auth'
import { useMemoryStore } from '@/stores/memory'
import { def } from '@vue/shared'
import { storeToRefs } from 'pinia'
import { computed, onMounted, ref } from 'vue'

const props = defineProps({
  select: {
    type: Number,
    default: 0,
  },
})

// 初始化店铺和状态
const agentStore = useAgentStore()
const authStore = useAuthStore()
const showBalanceAlert = ref(true)
const activeTab = ref(0)
const isLoading = ref(false)
const appStore = useAppStore()
const memoryStore = useMemoryStore()
const { loginStatus, userRoleInfo } = storeToRefs(authStore)
// 获取窗口高度
const windowHeight = computed(() => {
  return uni.getSystemInfoSync().windowHeight
})

// 计算scroll-view的高度
const scrollViewHeight = computed(() => {
  // 减去其他元素的高度（头部、标签等）
  // 可以根据实际情况调整减去的高度
  return windowHeight.value - 122 // 预估头部和标签的高度约为180px
})

// 分类标签
const categoryTabs = ['最近使用', '更多智能体']

// 用于存储获取到的智能体数据和总数
const allAgentsData = ref([])
const agentCount = ref(0)
const recentAppData = ref([])
const recentAppCount = ref(0)

// 分页相关数据
const allAgentsPageNum = ref(1)
const recentAppPageNum = ref(1)
const pageSize = ref(25)
const isLoadingMore = ref(false)
const hasNoMoreData = ref(false)
const recentNoMoreData = ref(false)
const categoryList = ref({
  type: 'cate',
  list: [],
})
async function reload() {
  console.log('重新加载')
  allAgentsPageNum.value = 1
  recentAppPageNum.value = 1
  hasNoMoreData.value = false
  recentNoMoreData.value = false
  try {
    isLoading.value = true
    await getCategoryList()

    // 获取当前空间最近使用的应用

    await getNowApps()
    // 获取当前空间所有应用
    await getAllApps()
  }
  catch (_error) {
    // 处理错误
    uni.showToast({
      title: '加载智能体失败',
      icon: 'none',
    })
  }
  finally {
    isLoading.value = false
  }
}
async function getCategoryList() {
  const result = await agentStore.getAppCategoryList(1, 50, true)
  if (result.count > 0) {
    categoryList.value.list = result.data.map((item: any) => {
      return {
        id: item.id,
        name: item.title,
        list: [],
      }
    })
  }
}

// 获取最近使用的应用
async function getNowApps() {
  const recentAppsresult = await agentStore.getRecentApps(recentAppPageNum.value, pageSize.value)

  if (recentAppPageNum.value === 1) {
    recentAppData.value = recentAppsresult.data.list
  }
  else {
    recentAppData.value = [...recentAppData.value, ...recentAppsresult.data.list]
  }
  recentAppCount.value = recentAppsresult.data.total
  if (recentAppData.value.length >= recentAppCount.value) {
    recentNoMoreData.value = true
  }

  if (recentAppCount.value > 0) {
    activeTab.value = 0
  }
}

// 获取全部可以使用的应用
async function getAllApps() {
  const availableAppsresult = await agentStore.getAvailableApps(allAgentsPageNum.value, pageSize.value)

  if (availableAppsresult.data.list.length > 0) {
    availableAppsresult.data.list.map((item: any) => {
      categoryList.value.list.map((cateItem: any) => {
        if (cateItem.id === item.category_id) {
          cateItem.list.push(item)
        }
      })
    })
  }

  if (allAgentsPageNum.value === 1) {
    allAgentsData.value = availableAppsresult.data.list
  }
  else {
    allAgentsData.value = [...allAgentsData.value, ...availableAppsresult.data.list]
  }
  agentCount.value = availableAppsresult.data.total
  if (allAgentsData.value.length >= agentCount.value) {
    hasNoMoreData.value = true
  }
}

watch(
  activeTab,
  async (newValue, oldValue) => {
    // console.log(newValue, oldValue, 'newValue, oldValue')

    if (!newValue) {
      await getNowApps()
    }
  },
  { immediate: true },
)

watch(
  () => loginStatus.value,
  (newVal) => {
    if (newVal) {
      reload()
    }
    else {
      allAgentsData.value = []
      recentAppData.value = []
    }
    uni.setNavigationBarTitle({
      title: userRoleInfo?.value?.current_company_name || '',
    })
  },
)

// 页面加载时获取数据
onLoad(async () => {
  if (loginStatus.value) {
    try {
      isLoading.value = true
      await getCategoryList()
      // 获取当前空间所有应用
      await getNowApps()
      // 获取当前空间最近使用的应用
      await getAllApps()

      await memoryStore.getUserMemoryList()

      if (props.select) {
        activeTab.value = props.select
      }
    }
    catch (_error) {
      // 处理错误
      uni.showToast({
        title: '加载智能体失败',
        icon: 'none',
      })
    }
    finally {
      isLoading.value = false
    }
  }
  else {
    allAgentsData.value = []
    recentAppData.value = []
  }
  uni.setNavigationBarTitle({
    title: userRoleInfo?.value?.current_company_name || '',
  })
})

// 根据当前选中的标签过滤智能体列表
const filteredAgents = computed(() => {
  if (activeTab.value === 0) {
    return recentAppData.value
  }
  else {
    return categoryList.value
  }
})

// 加载更多数据
async function loadMore() {
  console.log('选择了全部页', recentAppPageNum)
  console.log('选择页面', activeTab.value)

  isLoadingMore.value = true
  if (activeTab.value === 0 && !recentNoMoreData.value) {
    console.log('选择了全部页', recentAppPageNum)
    recentAppPageNum.value++
    await getNowApps()
  }
  else if ((activeTab.value === 1 && !hasNoMoreData.value)) {
    console.log('选择了当前页', allAgentsPageNum)
    allAgentsPageNum.value++

    await getAllApps()
  }
  isLoadingMore.value = false
}

onShareAppMessage((_res) => {
  return {
    title: 'AI智能员工 一人可抵百人用', // 标题
    path: '/pages/index/index', // 分享路径
    imageUrl: 'https://ctutangg91hkparu99tg.baseapi.memfiredb.com/storage/v1/object/public/icon/icon/share11.png', // 分享图
    // desc: '小程序描述描述描述描述',
  }
})

onShareTimeline(() => {
  return {
    title: 'AI智能员工 一人可抵百人用', // 标题
    path: '/pages/index/index', // 分享路径
    imageUrl: 'https://ctutangg91hkparu99tg.baseapi.memfiredb.com/storage/v1/object/public/icon/icon/share11.png', // 分享图
  }
})
</script>

<template>
  <view class="home-page">
    <!-- 顶部导航栏 -->
    <AppHeader :companies="userRoleInfo" />

    <!-- 余额提醒 -->
    <!-- <BalanceAlert v-if="showBalanceAlert" /> -->

    <!-- 页面内容区域 -->
    <view class="page-content">
      <!-- 标签切换 -->
      <view class="category-tabs-container">
        <view class="category-tabs">
          <view
            v-for="(tab, index) in categoryTabs" :key="index" class="tab-item"
            :class="{ active: activeTab === index }" @click="activeTab = index"
          >
            <text>{{ tab }}</text>
            <view v-if="activeTab === index" class="tab-indicator" />
          </view>
        </view>
      </view>

      <!-- 智能体列表 -->
      <scroll-view
        class="agents-container" scroll-y="true" :style="{ height: `${scrollViewHeight}px` }"
        @scrolltolower="loadMore"
      >
        <view class="agents-grid">
          <AgentCard :agentsCate="filteredAgents" @reLoad="reload" />
        </view>

        <!-- 加载更多提示 -->
        <view v-if="isLoadingMore" class="loading-more">
          <uni-icons type="loading" size="20" /> 加载中...
        </view>

        <!-- 空状态 -->
        <view
          v-if="(activeTab === 0 && recentAppCount === 0) || (activeTab === 1 && agentCount === 0)"
          class="empty-state" :style="{ height: `${scrollViewHeight - 30}px` }"
        >
          <uni-icons type="info" size="32" color="#CBD5E1" />
          <text class="empty-text">暂无智能体</text>
        </view>

        <!-- 没有更多数据提示 -->
        <view v-if="activeTab === 0 && recentNoMoreData" class="no-more-data">
          没有更多数据了
        </view>

        <view v-if="activeTab === 1 && hasNoMoreData" class="no-more-data">
          没有更多数据了
        </view>
        <!-- 底部支持信息 -->
        <Support />
      </scroll-view>
    </view>
  </view>
</template>

<style scoped>
/* 页面整体样式 */
.home-page {
  @apply bg-white;
}

/* 内容区域 */
.page-content {
}

/* 标签切换区域 */
.category-tabs-container {
  @apply mt-4 mb-2 mx-5;
}

.category-tabs {
  @apply flex flex-row items-center justify-start space-x-6;
}

.tab-item {
  @apply relative py-2 text-base text-gray-500 transition-all duration-200;
}

.tab-item.active {
  @apply text-blue-700 font-medium;
}

.tab-indicator {
  @apply absolute bottom-0 left-0 w-full h-1 bg-blue-600 rounded-full;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scaleX(0.5);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}

/* 搜索框 */
.search-container {
  @apply mb-6;
}

.search-box {
  @apply flex items-center bg-white rounded-full px-4 py-2.5 shadow-sm border border-gray-100;
}

.search-placeholder {
  @apply ml-2 text-sm text-gray-400;
}

/* 智能体列表 */
.agents-container {
  @apply relative;
  /* 高度通过计算动态设置 */
}

.agents-grid {
  @apply grid gap-4 px-4;
}

/* 空状态 */
.empty-state {
  @apply flex flex-col items-center justify-center py-16;
}

.empty-text {
  @apply mt-4 text-gray-400 text-sm;
}

/* 加载更多提示 */
.loading-more {
  @apply text-center text-gray-500 py-2;
}

/* 没有更多数据提示 */
.no-more-data {
  @apply text-center text-gray-400 pt-3 text-xs;
}
</style>
