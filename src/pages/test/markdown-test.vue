<template>
  <view class="test-page">
    <view class="header">
      <text class="title">Markdown Copy Test</text>
    </view>
    
    <view class="content">
      <UaMarkdown :source="testMarkdown" />
    </view>
    
    <view class="controls">
      <button @click="changeContent" class="test-btn">Change Content</button>
      <button @click="showConsole" class="test-btn">Show Console</button>
    </view>
  </view>
</template>

<script setup>
import UaMarkdown from '@/components/ua-markdown/ua-markdown.vue'

const testMarkdown = ref(`# Test Markdown Content

This is a test for the markdown copy functionality.

## Code Block Test

Here's a JavaScript code block:

\`\`\`javascript
function testFunction() {
  console.log('This is a test function');
  return 'Hello World';
}

const result = testFunction();
console.log(result);
\`\`\`

## Another Code Block

Here's a Python code block:

\`\`\`python
def hello_world():
    print("Hello, World!")
    return "Success"

result = hello_world()
print(f"Result: {result}")
\`\`\`

## Regular Text

This is regular text that should not have copy functionality.

**Bold text** and *italic text* should work normally.

- List item 1
- List item 2
- List item 3
`)

function changeContent() {
  testMarkdown.value = `# Updated Content

This is updated markdown content for testing.

\`\`\`json
{
  "name": "test",
  "version": "1.0.0",
  "description": "Test JSON for copy functionality"
}
\`\`\`

\`\`\`bash
echo "Testing bash commands"
ls -la
pwd
\`\`\`
`
}

function showConsole() {
  console.log('Current markdown content:', testMarkdown.value)
  uni.showToast({
    title: '检查控制台日志',
    icon: 'none'
  })
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  margin-bottom: 20px;
  text-align: center;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.content {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.controls {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.test-btn {
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 16px;
}

.test-btn:active {
  background-color: #0056CC;
}
</style>
