<script setup lang="ts">
import { supabase } from '@/services/supabase'
import { useAuthStore } from '@/stores/auth'
import { useMemoryStore } from '@/stores/memory'
import { useUsersStore } from '@/stores/user'
import { storeToRefs } from 'pinia'
import { computed, onMounted, ref } from 'vue'

const memoryStore = useMemoryStore()
const store = useAuthStore()
const userStore = useUsersStore()
const { user, userRoleInfo } = storeToRefs(store)
const { checkSession } = store

const fullName = computed({
  get: () => user.value?.user_metadata?.full_name || '微信用户',
  set: (value) => {
    if (user.value && user.value.user_metadata) {
      user.value.user_metadata.full_name = value
    }
  },
})

const fullNamePopup = ref(null)
const alertMemory = ref(null)
const tempName = ref('')
const alertMemoryTitle = ref('创建记忆')
const inputContent = ref('')
const deleteConfirmPopup = ref(null)
const maxLength = 300
const currentLength = computed(() => inputContent.value.length)

// 分页相关数据
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)
const isLoadingMore = ref(false)
const hasNoMoreData = ref(false)

const memoryList = ref()
const memoryItems = ref([])
async function getUserMemoryList(requestTyper?: string) {
  if (requestTyper === 'refresh') {
    pageNum.value = 1
    pageSize.value = total.value
  }

  try {
    console.log('获取列表')
    const results = await memoryStore.getUserMemoryList(pageNum.value, pageSize.value)
    console.log('查看数据', results)
    if (results) {
      memoryList.value = results
      if (pageNum.value === 1) {
        memoryItems.value = results.list
      }
      else {
        memoryItems.value = [...memoryItems.value, ...results.list]
      }
      total.value = results.total
    }
  }
  catch (error) {
    console.error('获取记忆列表出错:', error)
  }
  finally {
    isLoadingMore.value = false
  }
}

async function uploadAvatar(event: any) {
  try {
    uni.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      async success(res) {
        const fileExt = res.tempFiles[0].name.split('.').pop()
        const fileName = `${Date.now()}.${fileExt}`
        const uid = user.value?.id
        const filePath = `${uid}/${fileName}`
        const { error: uploadError } = await supabase.storage.from('avatars').upload(filePath, res.tempFiles[0])
        if (uploadError) {
          throw uploadError
        }

        const { data: { publicUrl }, error: publicUrlError } = supabase.storage.from('avatars').getPublicUrl(filePath)
        if (publicUrlError) {
          console.error('Public URL error:', publicUrlError)
          throw publicUrlError
        }
        if (!publicUrl) {
          console.error('Failed to get public URL')
          throw new Error('Failed to get public URL')
        }

        const { data: userData, error: updateError } = await supabase.auth.updateUser({
          data: { avatar_url: publicUrl, arvatar: publicUrl },
        })

        if (updateError) {
          console.error('Update user error:', updateError)
          throw updateError
        }
        store.updatePublicUser(userData.user, true)
        uni.showToast({
          title: '头像更新成功',
          duration: 2000,
        })

        await checkSession()
      },
    })
  }
  catch (error) {
    console.error('Error in uploadAvatar:', error)
    uni.showToast({
      title: '头像更新失败',
      duration: 2000,
    })
  }
}

async function uploadWxAvatar(event: any) {
  console.log('微信头像选择事件:', event)

  try {
    const tempFilePath = event.detail.avatarUrl
    if (!tempFilePath) {
      throw new Error('未获取到头像文件')
    }

    const fileTempPath = { tempFilePath }
    const fileExt = tempFilePath.split('.').pop()
    const uid = user.value?.id
    const fileName = `${Date.now()}.${fileExt}`
    const filePath = `${uid}/${fileName}`

    const { error: uploadError } = await supabase.storage.from('avatars').upload(filePath, fileTempPath, { upsert: true })
    if (uploadError) {
      throw uploadError
    }

    const { data: { publicUrl }, error: publicUrlError } = supabase.storage.from('avatars').getPublicUrl(filePath)
    if (publicUrlError) {
      console.error('Public URL error:', publicUrlError)
      throw publicUrlError
    }
    if (!publicUrl) {
      throw new Error('Failed to get public URL')
    }

    const { data: userData, error: updateError } = await supabase.auth.updateUser({
      data: { avatar_url: publicUrl },
    })
    if (updateError) {
      console.error('Update user error:', updateError)
      throw updateError
    }
    store.updatePublicUser(userData.user, true)
    uni.showToast({
      title: '头像更新成功',
      duration: 2000,
    })

    await checkSession()
  }
  catch (error) {
    console.error('Error in uploadWxAvatar:', error)
    uni.showToast({
      title: '头像更新失败',
      duration: 2000,
    })
  }
}

function openFullNamePup(type) {
  console.log(type, 'typetypetype')

  tempName.value = fullName.value
  fullNamePopup.value?.open(type)
}

async function handleFullNameChange() {
  console.log(tempName.value, 'tempNametempNametempNametempName')
  try {
    const { data: userData, error } = await supabase.auth.updateUser({
      data: {
        full_name: tempName.value,
        nickname: tempName.value,
      },
    })
    if (error) {
      throw error
    }
    else {
      fullName.value = tempName.value
      fullNamePopup.value?.close()

      store.updatePublicUser(userData.user, true)

      await checkSession()
    }
  }
  catch (error) {
    console.error('Error updating full name:', error)
  }
  finally {
    fullNamePopup.value?.close()
    tempName.value = ''
  }
}

function addToMemory() {
  alertMemoryTitle.value = '创建记忆'
  alertMemory.value?.open()
}

function resetInsertOrUpdateMemoryData() {
  insertOrUpdateMemoryData.value = {
    id: null,
    key: '',
    value: '',
    is_enable: true,
    uid: null,
    flag: '',
  }
}

const insertOrUpdateMemoryData = ref({
  id: null,
  key: '',
  value: '',
  is_enable: true,
  uid: null,
  flag: '',
})

function editMemory(item) {
  alertMemoryTitle.value = '编辑记忆'
  insertOrUpdateMemoryData.value.id = item.id || null
  if (item.flag) {
    insertOrUpdateMemoryData.value.key = getChineseValue(item.flag)
  }
  else {
    insertOrUpdateMemoryData.value.key = item.key || ''
  }
  insertOrUpdateMemoryData.value.value = item.value || ''
  insertOrUpdateMemoryData.value.is_enable = item.is_enable || true
  insertOrUpdateMemoryData.value.uid = item.uid || null
  insertOrUpdateMemoryData.value.flag = item.flag || ''
  inputContent.value = item.value || ''
  alertMemory.value?.open()
}

const mapping = {
  job: '职业',
  industry: '行业',
  feature: '优势特色',
  service: '核心产品/服务',
}

function getChineseValue(key: string) {
  return mapping[key] || ''
}

function memoryClose() {
  inputContent.value = ''
  alertMemory.value?.close()
  resetInsertOrUpdateMemoryData()
}

const isUserMember = ref(false)

async function handleMemoryToggle(event) {
  const newValue = event.detail.value
  await memoryStore.companyUpdateUserMemorySetting(newValue)
  userRoleInfo.value.is_use_memory = newValue
  await fetchUsers()
}

async function memoryPost() {
  const content = inputContent.value
  try {
    await memoryStore.upsertUserMemory(
      insertOrUpdateMemoryData.value.key,
      content,
      insertOrUpdateMemoryData.value.flag,
      insertOrUpdateMemoryData.value.uid,
      insertOrUpdateMemoryData.value.is_enable,
      insertOrUpdateMemoryData.value.id,
    )
  }
  catch (err) {
    console.error('记忆操作出错:', err)
    uni.showToast({
      title: '操作失败，请稍后重试',
      icon: 'none',
      duration: 2000,
    })
  }
  memoryClose()
  resetInsertOrUpdateMemoryData()
  await getUserMemoryList('refresh')
}

onReachBottom(async () => {
  if (isLoadingMore.value || hasNoMoreData.value) {
    return
  }
  if (pageNum.value * pageSize.value >= total.value) {
    hasNoMoreData.value = true
    return
  }
  isLoadingMore.value = true
  pageNum.value++
  await getUserMemoryList()
})

async function fetchUsers() {
  const result = await userStore.getCurrentUserInfo()
  if (result) {
    isUserMember.value = result.is_use_memory
  }
}

onMounted(async () => {
  await checkSession()
  await fetchUsers()
  await getUserMemoryList()
})

// 存储待删除的记忆 ID
const deleteMemory = ref('')
// 显示删除确认弹窗
function showDeleteConfirm(id: string) {
  deleteMemory.value = id
  deleteConfirmPopup.value?.open()
}
// 关闭删除确认弹窗
function closeDeleteConfirm() {
  deleteConfirmPopup.value?.close()
}
// 确认删除记忆
async function confirmDelete() {
  try {
    await memoryStore.deletUserMemory(deleteMemory.value)
    await getUserMemoryList('refresh')
    uni.showToast({
      title: '删除成功',
      icon: 'success',
      duration: 2000,
    })
  }
  catch (error) {
    console.error('删除出错:', error)
    uni.showToast({
      title: '删除失败，请稍后重试',
      icon: 'none',
      duration: 2000,
    })
  }
  closeDeleteConfirm()
}
</script>

<template>
  <view class="min-h-screen bg-gray-100 px-4">
    <!-- Profile Section -->
    <view class="flex flex-col items-center py-2 ">
      <view class="relative">
        <view class="size-[70px] overflow-hidden rounded-full border-solid bg-gray-200">
          <image
            :src="user?.user_metadata.avatar_url || '/static/images/author.png'" alt="Profile avatar"
            class="size-full object-cover"
          />
        </view>
        <!-- #ifdef MP-WEIXIN -->
        <button
          open-type="chooseAvatar" type="default"
          class="absolute -bottom-2 -right-2 flex size-[28px] items-center justify-center rounded-full border-none bg-white shadow-md"
          @chooseavatar="uploadWxAvatar"
        >
          <uni-icons type="camera-filled" size="20" class="items-center justify-center" />
        </button>
        <!-- #endif -->

        <!-- #ifdef H5 -->
        <button
          type="default"
          class="absolute -bottom-2 -right-2 flex size-[28px] items-center justify-center rounded-full border-none bg-white shadow-md"
          @click="uploadAvatar"
        >
          <uni-icons type="camera-filled" size="20" class="items-center justify-center" />
        </button>
        <!-- #endif -->
      </view>
      <view class="relative mt-4 flex items-center">
        <view class=" text-base text-gray-600">
          <view class="text-center text-base text-gray-600" @click="openFullNamePup">
            {{ fullName }}
          </view>
        </view>
        <!-- <view class="absolute right-[calc(100%-200px)] ml-3 text-gray-400">
          8/16
        </view> -->
      </view>
    </view>

    <view v-if="isUserMember !== undefined">
      <!-- 这里是原有的开关相关代码 -->
      <view class="shadow-xs mt-2 rounded-lg bg-white p-3">
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <uni-icons type="fire-filled" size="20" class="mr-1" color="#3B82F6" />
            <view class="text-base">
              在聊天中使用记忆
            </view>
          </view>
          <label class="relative inline-flex  items-center">
            <!-- 编写一个开关按钮  双向绑定isUserMember  然后 访问handleMemoryToggle方法 -->
            <switch :checked="isUserMember" color="#3B82F6" style="transform:scale(0.7)" @change="handleMemoryToggle" />

          </label>
        </view>
        <view class="mt-1 text-xs text-blue-400">
          开启记忆功能，享受极速个性化回答
        </view>
      </view>
    </view>

    <!-- Memory List -->
    <view class="shadow-xs mt-2 rounded-lg bg-white p-3">
      <view class="mb-4 flex items-center justify-between border-b border-gray-300 pb-2">
        <view class="text-lg font-medium">
          记忆列表
        </view>
        <view class="flex items-center text-blue-500" @click="addToMemory">
          <uni-icons type="plusempty" size="16" color="#3B82F6" class="ml-2" />
          <view>新增记忆</view>
        </view>
      </view>

      <!-- Memory Items -->
      <view class="space-y-4">
        <view
          v-for="(item, index) in memoryItems" :key="index" class="w-full border-b border-gray-100 pb-2"
          @click="editMemory(item)"
        >
          <view class="flex flex-row items-center justify-between">
            <view class="max-w-64">
              <view class="truncate font-medium text-black">
                {{ getChineseValue(item.flag) }}
              </view>
              <view class="mt-1  truncate text-sm text-gray-500">
                {{ item.value }}
              </view>
            </view>
            <view class="flex flex-row items-baseline justify-end">
              <uni-icons type="more-filled" size="15" class="m-auto" color="#9CA3AF" />
              <uni-icons type="trash" size="15" class="m-auto" color="#9CA3AF" @click="showDeleteConfirm(item.id)" />
            </view>
          </view>
        </view>
      </view>
      <!-- 加载更多提示 -->
      <view v-if="isLoadingMore" class="py-2 text-center text-gray-500">
        <uni-icons type="loading" size="20" /> 加载中...
      </view>
      <!-- 没有更多数据提示 -->
      <view v-if="hasNoMoreData" class="py-2 text-center text-gray-500">
        没有更多数据了
      </view>
    </view>
  </view>

  <uni-popup
    ref="fullNamePopup" type="bottom" safe-area="false" background-color="#ffffff"
    borderRadius="20px 20px 0px 0px"
  >
    <view class="popup-header flex items-center justify-between  px-4 py-2">
      修改用户昵称
    </view>
    <view class="popup-content flex h-40 ">
      <view class="flex  w-full flex-col items-center justify-center">
        <view class="border-b text-center">
          <input v-model="tempName" class="h-6 w-40 text-lg" type="nickname">
        </view>
        <view class="mt-4">
          <button class="h-10 w-32 bg-blue-500 text-white" @click="handleFullNameChange">
            保存
          </button>
        </view>
      </view>
    </view>
  </uni-popup>

  <!-- 提示窗示例 -->
  <uni-popup ref="alertMemory" type="dialog" background-color="#ffffff" borderRadius="20px 20px 20px 20px">
    <uni-popup-dialog
      mode="input" class="!w-96" :title="alertMemoryTitle" :before-close="true" @close="memoryClose"
      @confirm="memoryPost"
    >
      <view class="flex h-28 w-full flex-col items-center justify-center rounded-lg bg-gray-100 p-2 text-sm">
        <textarea
          v-model="inputContent" class="w-full" :maxlength="maxLength" placeholder="您所感兴趣的领域或偏好的回答格式"
          placeholder-class="text-gray-300 text-sm"
        />
        <view class="mt-2 flex w-full justify-between text-xs text-gray-400">
          <view>超出字数部分不会生效</view>
          <view>{{ currentLength }}/{{ maxLength }}</view>
        </view>
      </view>

      <view />
    </uni-popup-dialog>
  </uni-popup>
  <!-- 删除确认弹窗 -->
  <uni-popup ref="deleteConfirmPopup" type="dialog" background-color="#ffffff" borderRadius="20px 20px 20px 20px">
    <uni-popup-dialog
      mode="default" class="!w-96" title="确认删除" :before-close="true"
      @close="closeDeleteConfirm" @confirm="confirmDelete"
    >
      <view class="p-6 text-center">
        是否删除此记忆？
      </view>
    </uni-popup-dialog>
  </uni-popup>
</template>
