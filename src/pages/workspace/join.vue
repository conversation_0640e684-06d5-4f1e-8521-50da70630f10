<script setup lang="ts">
import PhoneBindPopup from '@/components/common/PhoneBindPopup.vue'
import { useAuthStore } from '@/stores/auth'
import { useInviteLinkStore } from '@/stores/workspace'
import { ref } from 'vue'

const props = defineProps({
  scene: {
    type: String,
    default: '',
  },
})
// 获取邀请链接相关功能
const inviteLinkStore = useInviteLinkStore()
const { joinCompanyByInviteLink } = inviteLinkStore
const authStore = useAuthStore()

// 表单状态
const isLoading = ref(false)
const formError = ref('')
const formSuccess = ref(false)

const { user, loginStatus } = storeToRefs(authStore)

// 表单数据
const inviteCode = ref('')

// 手机号绑定相关状态
const showPhoneBindPopup = ref(false)

// 验证表单
function validateForm() {
  if (!inviteCode.value.trim()) {
    formError.value = '请输入邀请码'
    return false
  }
  return true
}
// 检查用户是否已绑定手机号
function isPhoneBound() {
  return user.value?.phone && user.value.phone !== ''
}

// 处理手机号绑定成功
function handlePhoneBindSuccess() {
  // 绑定成功后继续加入工作空间流程
  setTimeout(() => {
    joinWorkspace()
  }, 1000)
}

onMounted(async () => {
  // 获取小程序码的scene参数
  setTimeout(() => {
    const scene = props.scene

    if (scene) {
      // 假设scene参数就是邀请码，直接赋值
      inviteCode.value = scene
      console.log(scene, 'scenescenescenescene')
    }

    if (loginStatus.value === false) {
      uni.navigateTo({
        url: '/pages/auth/login',
      })
      return
    }
    // 检查用户是否已绑定手机号，如果没有绑定则显示绑定弹窗
    if (!isPhoneBound()) {
      showPhoneBindPopup.value = true
    }
  }, 800)
})

// 加入工作空间
async function joinWorkspace() {
  // 检查用户是否已绑定手机号
  if (!isPhoneBound()) {
    showPhoneBindPopup.value = true
    return
  }

  // 验证表单
  if (!validateForm()) {
    uni.showToast({
      title: formError.value,
      icon: 'none',
      duration: 2000,
    })
    return
  }

  try {
    isLoading.value = true

    // 显示加载中
    // uni.showLoading({ title: '正在加入...' })
    // console.log('获取的sssuid', uid.value)
    // 调用加入空间的API
    await joinCompanyByInviteLink(inviteCode.value.trim(), user.value.id)

    // 加入成功
    formSuccess.value = true
    authStore.checkUserInfo(user.value.id)
    // 显示成功提示
    uni.hideLoading()
    uni.showToast({
      title: '成功加入工作空间',
      icon: 'success',
      duration: 2000,
    })

    // 加入成功后跳转到首页
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/index/index?select=1',
      })
    }, 2000)
  }
  catch (error: any) {
    uni.hideLoading()
    formError.value = error.message || '加入失败，请重试'
    uni.showToast({
      title: formError.value,
      icon: 'none',
      duration: 2000,
    })
  }
  finally {
    isLoading.value = false
  }
}
</script>

<template>
  <view class="join-workspace-page">
    <!-- 页面背景装饰 -->
    <view class="page-decoration">
      <view class="decoration-circle-1" />
      <view class="decoration-circle-2" />
    </view>

    <!-- 表单内容区 -->
    <view class="form-container">
      <view class="form-header">
        <view class="form-subtitle">
          通过邀请码加入已有的工作空间
        </view>
      </view>

      <!-- 邀请码区域 -->
      <view class="form-section">
        <view class="section-title">
          邀请码
        </view>

        <view class="form-group">
          <label class="form-label">输入邀请码</label>
          <view class="input-container">
            <uni-icons type="staff-filled" size="18" color="#3B82F6" class="input-icon" />
            <input v-model="inviteCode" class="form-input" placeholder="请输入邀请码">
          </view>
          <view class="form-tip">
            请输入您收到的工作空间邀请码
          </view>
        </view>
      </view>

      <!-- 说明区域 -->
      <view class="form-section">
        <view class="section-title">
          说明
        </view>

        <view class="info-container">
          <view class="info-item">
            <uni-icons type="info-filled" size="16" color="#3B82F6" class="info-icon" />
            <text class="info-text">加入工作空间后，您将可以访问该空间内的所有资源</text>
          </view>
          <view class="info-item">
            <uni-icons type="info-filled" size="16" color="#3B82F6" class="info-icon" />
            <text class="info-text">您可以随时切换或退出已加入的工作空间</text>
          </view>
          <view class="info-item">
            <uni-icons type="info-filled" size="16" color="#3B82F6" class="info-icon" />
            <text class="info-text">如果您没有邀请码，请联系工作空间管理员获取</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮区域 -->
    <view class="submit-button-container">
      <button class="submit-button" :disabled="isLoading" @click="joinWorkspace">
        <text class="button-text">加入工作空间</text>
      </button>
    </view>
  </view>

  <!-- 手机号绑定弹窗组件 -->
  <PhoneBindPopup :visible="showPhoneBindPopup" @onSuccessCallback="handlePhoneBindSuccess" />
</template>

<style scoped>
/* 页面整体样式 */
.join-workspace-page {
  @apply pb-24 min-h-screen relative;
  background: linear-gradient(to bottom, #f0f9ff, #e0f2fe);
}

/* 装饰元素 */
.page-decoration {
  @apply absolute inset-0 overflow-hidden pointer-events-none;
  z-index: 0;
}

.decoration-circle-1 {
  @apply absolute rounded-full bg-blue-500 opacity-5;
  width: 300px;
  height: 300px;
  top: -100px;
  right: -100px;
}

.decoration-circle-2 {
  @apply absolute rounded-full bg-blue-500 opacity-5;
  width: 200px;
  height: 200px;
  bottom: 20%;
  left: -100px;
}

/* 表单容器 */
.form-container {
  @apply p-5 relative z-10;
}

/* 表单头部 */
.form-header {
  @apply mb-6 text-center;
}

.form-title {
  @apply text-xl font-bold text-gray-800 mb-1;
}

.form-subtitle {
  @apply text-sm text-gray-500;
}

/* 表单分区 */
.form-section {
  @apply mb-8 bg-white rounded-xl p-5 shadow-sm;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.section-title {
  @apply text-base font-medium text-gray-800 mb-4 flex items-center;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  @apply absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-5 bg-blue-500 rounded-full;
}

/* 表单组件 */
.form-group {
  @apply mb-5;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-tip {
  @apply mt-2 text-xs text-gray-500;
}

/* 输入框容器 */
.input-container {
  @apply flex items-center w-full px-4 py-2 border border-gray-200 rounded-lg bg-gray-50;
  transition: all 0.2s ease;
}

.input-container:focus-within {
  @apply border-blue-500 bg-white;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.input-icon {
  @apply mr-2;
}

.form-input {
  @apply w-full py-2 bg-transparent focus:outline-none text-gray-800;
}

/* 信息容器 */
.info-container {
  @apply flex flex-col space-y-3;
}

.info-item {
  @apply flex items-start;
}

.info-icon {
  @apply mr-2 mt-0.5;
}

.info-text {
  @apply text-sm text-gray-600;
}

/* 提交按钮 */
.submit-button-container {
  @apply px-5 mb-8 relative z-10;
}

.submit-button {
  @apply w-full flex items-center justify-center py-3 px-4 rounded-lg bg-blue-500 text-white font-medium;
  transition: all 0.2s ease;
}

.submit-button:hover {
  @apply bg-blue-600;
}

.submit-button:active {
  @apply bg-blue-700;
}

.button-text {
  @apply ml-2;
}
</style>
