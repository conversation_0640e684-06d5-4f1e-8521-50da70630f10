<script setup lang="ts">
// pages/main/index.ts
import { supabase } from '@/services/supabase'
import { useAuthStore } from '@/stores/auth'
import { globalDataStore } from '@/stores/globalData'

const props = defineProps({
  loginMode: {
    type: String,
    default: 'wx',
  },
})
const platform = ref('')
const changeH5 = ref(false)
const store = useAuthStore()
const { loginStatus, agreed } = storeToRefs(store)
const { loginWx, loginH5 } = store
const phoneNumber = ref()
const VerifCode = ref()
const remainingTime = ref(0)
const isActive = ref(false)
const sendButtonText = ref('发送验证码')
let timer: any = null
const globalStore = globalDataStore()

const { Platform } = storeToRefs(globalStore)
function checkboxChange() {
  agreed.value = !agreed.value
}
async function sendCode() {
  // 新增手机号格式验证
  if (!/^1\d{10}$/.test(phoneNumber.value)) {
    uni.showToast({
      icon: 'none',
      title: '请输入有效的手机号',
      duration: 2000,
    })
    return
  }

  if (remainingTime.value > 0) { return }
  if (!agreed.value) {
    uni.showToast({
      title: '请先同意用户协议和隐私政策',
      duration: 2000,
    })
    return
  }
  let { data, error } = await supabase.auth.signInWithOtp({
    phone: phoneNumber.value,
  })
  if (error) {
    console.error('Error sending verification code:', error)
  }
  else {
    uni.showToast({
      title: '验证码发送成功',
      duration: 2000,
    })
    isActive.value = true
    remainingTime.value = 60
    clearInterval(timer) // 删除:可能存在的旧定时器
    timer = setInterval(() => {
      if (remainingTime.value > 0) {
        remainingTime.value--
        sendButtonText.value = `${remainingTime.value}秒`
      }
      else {
        clearInterval(timer)
        sendButtonText.value = '发送验证码'
        isActive.value = false
        remainingTime.value = 0
      }
    }, 1000)
  }
}
function GoToDeclaration(page: string) {
  uni.navigateTo({
    url: `/subdeclaration/declaration/${page}`,
  })
}

onMounted(() => {
  platform.value = Platform.value
  if (props.loginMode === 'h5') {
    changeH5.value = true
  }
  if (loginStatus.value) {
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
})
function navBack() {
  uni.reLaunch({
    url: '/pages/my/index',
  })
}
onUnmounted(() => {
  clearInterval(timer)
})
</script>

<template>
  <view
    v-if="platform === 'web' || platform === 'h5' || changeH5"
    class="flex h-screen flex-col items-center justify-center bg-gray-50 px-4"
  >
    <view class="w-full max-w-md rounded-lg bg-white p-6 shadow-md">
      <form class="flex flex-col">
        <view>
          <input
            v-model="phoneNumber" type="tel"
            class="mb-4 h-auto rounded-md border-0 bg-gray-100 p-2 text-gray-900 transition duration-150 ease-in-out focus:bg-gray-200 focus:outline-none focus:ring-1 focus:ring-blue-500"
            placeholder="手机号"
          >
        </view>
        <view class="flex flex-row">
          <view class="flex-auto">
            <input
              v-model="VerifCode" type="number"
              class="h-auto rounded-md border-0 bg-gray-100 p-2 text-gray-900 transition duration-150 ease-in-out focus:bg-gray-200 focus:outline-none focus:ring-1 focus:ring-blue-500"
              placeholder="验证码"
            >
          </view>
          <view
            :class="`flex-2 ml-3 rounded-md border-0 ${isActive ? 'bg-gray-300' : 'bg-blue-500'} p-2 text-sm text-white transition duration-150 ease-in-out focus:bg-gray-200 focus:outline-none focus:ring-1 focus:ring-blue-500` "
            @click="sendCode"
          >
            {{ sendButtonText }}
          </view>
        </view>

        <view class="mt-4">
          <view class="flex justify-center">
            <label class="text-sm">
              <checkbox-group @change="checkboxChange">
                <checkbox :checked="agreed" color="#FFCC33" style="transform:scale(0.7)" />我已阅读并同意
              </checkbox-group>
            </label>
            <view class="text-sm text-gray-500">
              <text class="text-blue-500" @click="GoToDeclaration('agreement')">用户协议</text>
              和
              <text class="text-blue-500" @click="GoToDeclaration('privacyPolicy')">隐私政策</text>
            </view>
          </view>

          <view
            class="mt-4 w-full rounded-md bg-gradient-to-r from-indigo-500 to-blue-500 px-4 py-2 text-center font-bold text-white transition duration-150 ease-in-out hover:bg-indigo-600 hover:to-blue-600"
            @click="loginH5(phoneNumber, VerifCode)"
          >
            登录
          </view>
        </view>
        <view v-if="changeH5" class="mt-3 flex flex-col items-center justify-center">
          <view class="text-gray-500">
            或
          </view>
          <view>
            <button
              class="flex items-center justify-center rounded-md bg-gradient-to-r from-green-400 to-green-600 px-6 py-3 text-white shadow-md transition duration-300 hover:shadow-lg"
              type="primary" @click="loginWx"
            >
              <text class="mr-2 text-lg">微信快速登录</text>
            </button>
          </view>
        </view>
      </form>
    </view>
  </view>
  <view v-else class="flex h-screen flex-col items-center justify-center bg-gray-50 px-4">
    <view class="w-full max-w-md rounded-lg bg-white p-6 shadow-md">
      <view class="mb-16 text-center">
        <view class="mb-10 text-xl">
          🎉欢迎进入
        </view>
        <text class="text-xl font-bold text-gray-800">智能员工平台</text>
      </view>
      <view class="mb-3 mt-4 flex justify-center">
        <label class="text-sm">
          <checkbox-group @change="checkboxChange">
            <checkbox :checked="agreed" color="#FFCC33" style="transform:scale(0.7)" />我已阅读并同意
          </checkbox-group>
        </label>
        <view class="text-sm text-gray-500">
          <text class="text-blue-500" @click="GoToDeclaration('agreement')">用户协议</text>
          和
          <text class="text-blue-500" @click="GoToDeclaration('privacyPolicy')">隐私政策</text>
        </view>
      </view>
      <view class="flex justify-center">
        <button
          class="flex items-center justify-center rounded-md bg-gradient-to-r from-green-400 to-green-600 px-20 py-3 text-white shadow-md transition duration-300 hover:shadow-lg"
          type="primary" @click="loginWx"
        >
          <text class="text-lg">立即进入</text>
        </button>
      </view>
      <view class="my-4 flex justify-center">
        <view @click="navBack">
          暂不进入
        </view>
      </view>
      <view class="mt-3 flex flex-col items-center justify-center">
        <view class="mt-2  text-base text-blue-500" @click="changeH5 = true">
          使用手机号登录
        </view>
      </view>
    </view>
  </view>
</template>
