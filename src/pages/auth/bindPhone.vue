<script setup lang="ts">
import { supabase } from '@/services/supabase'
import { useAuthStore } from '@/stores/auth'
import { storeToRefs } from 'pinia'
import { computed, onMounted, ref } from 'vue'

const props = defineProps({
  workspaceName: {
    type: String,
    default: '',
  },
})

const authStore = useAuthStore()
const { user, agreed } = storeToRefs(authStore)
const loading = ref(false)
const bindSuccess = ref(false)
const errorMessage = ref('')

// 步骤控制
const currentStep = ref(1) // 1: 绑定手机号, 2: 更新用户信息
const totalSteps = 2
// 用户信息表单
const userInfo = ref({
  fullName: '',
  avatar: '',
})

// 计算当前步骤的进度百分比
const progressPercentage = computed(() => {
  return (currentStep.value / totalSteps) * 100
})
// 进入下一步
function goToNextStep() {
  if (currentStep.value < totalSteps) {
    currentStep.value++
  }
}

// 返回上一步
// 注意：此功能暂未使用，但保留以备将来需要
// function goToPreviousStep() {
//   if (currentStep.value > 1) {
//     currentStep.value--
//   }
// }

// 同意协议状态
function checkboxChange() {
  agreed.value = !agreed.value
}

// 跳转到协议页面
function GoToDeclaration(page: string) {
  uni.navigateTo({
    url: `/pages/declaration/${page}`,
  })
}

function finish() {
  if (userInfo.value.avatar !== '' && userInfo.value.fullName !== '微信用户' && userInfo.value.fullName !== '') {
    uni.showToast({
      title: '信息更新成功',
      icon: 'success',
      duration: 2000,
    })
    // 完成所有步骤，返回首页
    setTimeout(() => {
      uni.navigateTo({
        url: '/pages/workspace/join',
      })
    }, 2000)
  }
}

// 获取微信手机号
async function getPhoneNumber(e: any) {
  // 检查是否同意协议

  if (e.type === 'tap') {
    if (!agreed.value) {
      uni.showToast({
        icon: 'none',
        title: '请先同意用户协议和隐私政策',
        duration: 1000,
      })
      return
    }

    return
  }

  // 如果没有获取到code，说明用户拒绝授权
  if (e.detail.errMsg !== 'getPhoneNumber:ok') {
    uni.showToast({
      icon: 'none',
      title: '获取手机号失败，请重试',
      duration: 1000,
    })
    return
  }

  loading.value = true
  errorMessage.value = ''

  // 显示加载中提示
  uni.showLoading({
    title: '绑定中...',
    mask: true,
  })

  // 获取微信手机号码
  const { code } = e.detail
  try {
    // 注意：这里假设 supabase.auth 有 wechatBindPhone 方法
    // 如果实际项目中没有这个方法，需要根据实际API调整
    // 这里仅作为示例

    const { data: wxData, error: phoneError } = await supabase.auth.wechatBindPhone({ code })
    console.log(phoneError, 'phoneError.data.code', wxData)
    bindSuccess.value = true
    authStore.updatePublicUser(wxData.user, true)

    if (!wxData.user) {
      throw new Error('绑定手机号失败')
    }
    else {
      bindSuccess.value = true
      authStore.updatePublicUser(wxData.user, true)

      // 更新用户信息表单的初始值
      if (wxData.user && wxData.user.user_metadata) {
        userInfo.value.fullName = wxData.user.user_metadata.full_name || ''
        userInfo.value.avatar = wxData.user.user_metadata.avatar_url || ''
      }

      // 进入下一步而不是跳转页面
      goToNextStep()

      uni.showToast({
        title: '手机号绑定成功',
        icon: 'success',
        duration: 1000,
      })
    }
  }
  catch (_error) {
    uni.showToast({
      title: '此电话号码的用户已经注册',
      icon: 'none',
      duration: 1000,
    })
  }
  finally {
    loading.value = false
    uni.hideLoading()
  }
}

// 上传头像
async function uploadAvatar(event: any) {
  try {
    uni.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      async success(res) {
        const fileExt = res.tempFiles[0].name.split('.').pop()
        const fileName = `${Date.now()}.${fileExt}`
        const uid = user.value?.id
        const filePath = `${uid}/${fileName}`
        const { error: uploadError } = await supabase.storage.from('avatars').upload(filePath, res.tempFiles[0])
        if (uploadError) {
          throw uploadError
        }

        const { data: { publicUrl }, error: publicUrlError } = supabase.storage.from('avatars').getPublicUrl(filePath)
        if (publicUrlError) {
          console.error('Public URL error:', publicUrlError)
          throw publicUrlError
        }
        if (!publicUrl) {
          console.error('Failed to get public URL')
          throw new Error('Failed to get public URL')
        }

        const { data: userData, error: updateError } = await supabase.auth.updateUser({
          data: { avatar_url: publicUrl, arvatar: publicUrl },
        })

        if (updateError) {
          console.error('Update user error:', updateError)
          throw updateError
        }
        authStore.updatePublicUser(userData.user, true)
        uni.showToast({
          title: '头像更新成功',
          duration: 1000,
        })

        await authStore.checkSession()

        finish()
      },
    })
  }
  catch (error) {
    console.error('Error in uploadAvatar:', error)
    uni.showToast({
      title: '头像更新失败',
      duration: 1000,
    })
  }
}

// 上传微信头像
async function uploadWxAvatar(event: any) {
  console.log('微信头像选择事件:', event)

  try {
    const tempFilePath = event.detail.avatarUrl
    if (!tempFilePath) {
      throw new Error('未获取到头像文件')
    }

    const fileTempPath = { tempFilePath }
    const fileExt = tempFilePath.split('.').pop()
    const uid = user.value?.id
    const fileName = `${Date.now()}.${fileExt}`
    const filePath = `${uid}/${fileName}`

    const { error: uploadError } = await supabase.storage.from('avatars').upload(filePath, fileTempPath, { upsert: true })
    if (uploadError) {
      throw uploadError
    }

    const { data: { publicUrl }, error: publicUrlError } = supabase.storage.from('avatars').getPublicUrl(filePath)
    if (publicUrlError) {
      console.error('Public URL error:', publicUrlError)
      throw publicUrlError
    }
    if (!publicUrl) {
      throw new Error('Failed to get public URL')
    }

    const { data: userData, error: updateError } = await supabase.auth.updateUser({
      data: { avatar_url: publicUrl },
    })
    if (updateError) {
      console.error('Update user error:', updateError)
      throw updateError
    }
    authStore.updatePublicUser(userData.user, true)
    uni.showToast({
      title: '头像更新成功',
      duration: 1000,
    })
    userInfo.value.avatar = publicUrl
    await authStore.checkSession()

    finish()
  }
  catch (error) {
    console.error('Error in uploadWxAvatar:', error)
    uni.showToast({
      title: '头像更新失败',
      duration: 1000,
    })
  }
}

// 更新用户信息
async function updateUserInfo(e: any) {
  console.log(e.detail.value, 'updateUserInfo', userInfo.value.fullName)

  if (e.detail.value === '微信用户') { return }
  try {
    loading.value = true

    // 显示加载中提示
    uni.showLoading({
      title: '更新中...',
      mask: true,
    })

    const { data: userData, error } = await supabase.auth.updateUser({
      data: {
        full_name: e.detail.value,
        nickname: e.detail.value,

      },
    })

    if (error) {
      throw error
    }
    userInfo.value.fullName = e.detail.value
    authStore.updatePublicUser(userData.user, true)

    uni.showToast({
      title: '信息更新成功',
      icon: 'success',
      duration: 1000,
    })

    finish()
  }
  catch (_error) {
    uni.showToast({
      title: '更新失败，请重试',
      icon: 'none',
      duration: 1000,
    })
  }
  finally {
    loading.value = false
    uni.hideLoading()
  }
}

// 跳过绑定手机
function skipBindPhone() {
  uni.showModal({
    title: '提示',
    content: '是否确定跳过绑定手机号？ 绑定手机号有助于获得更好的使用体验。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack({
          delta: 1,
        })
      }
    },
  })
}

// 跳过更新用户信息
function skipUserInfoUpdate() {
  uni.showModal({
    title: '提示',
    content: '是否确定跳过用户信息更新？完善信息有助于获得更好的使用体验。',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack({
          delta: 1,
        })
      }
    },
  })
}

onMounted(() => {
  uni.hideHomeButton()
  // 检查用户是否已登录
  if (!authStore.loginStatus) {
    uni.navigateTo({
      url: '/pages/auth/login',
    })
    return
  }

  // 检查用户是否已绑定手机号
  if (user?.value.phone !== '') {
    // 如果已绑定手机号，直接进入第二步
    bindSuccess.value = true
    currentStep.value = 2

    // 更新用户信息表单的初始值
    if (user.value && user.value.user_metadata) {
      userInfo.value.fullName = user.value.user_metadata.full_name || ''
      userInfo.value.avatar = user.value.user_metadata.avatar_url || ''
    }
  }
})
</script>

<template>
  <view class="bind-phone-container">
    <view class="bind-phone-content">
      <view class="mb-14 flex flex-col items-center justify-center">
        <view class="text-2xl">
          🎉欢迎加入
        </view>
        <view class="text-2xl">
          {{ workspaceName !== "" ? workspaceName : '智能' }}工作空间
        </view>
      </view>
      <!-- 步骤指示器 -->
      <view class="steps-container">
        <view class="steps-progress">
          <view v-if="currentStep === 1" class="mb-3 w-full text-center text-xl font-bold text-gray-600">
            验证手机号 保障账号安全
          </view>
          <view v-if="currentStep === 2" class="mb-3 w-full text-center text-xl font-bold  text-gray-600">
            同步个性资料 解锁专属服务
          </view>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: `${progressPercentage}%` }" />
          </view>
        </view>
        <view class="steps-labels">
          <view class="step-label" :class="{ active: currentStep >= 1 }">
            绑定手机号
          </view>
          <view class="step-label" :class="{ active: currentStep >= 2 }">
            更新用户信息
          </view>
        </view>
      </view>

      <!-- 步骤1: 绑定手机号 -->
      <view v-if="currentStep === 1" class="step-content">
        <view class="header">
          <view class="title" />
          <view class="subtitle">
            <view class="inline-table text-left text-base font-bold ">
              您的手机号将用于:
            </view>
            <view class="text-base before:content-['·']">
              用于账号统一验证
            </view>
            <view class="text-base before:content-['·']">
              在空间中专属权益
            </view>
          </view>
        </view>

        <!-- 绑定表单 -->
        <view class="bind-form">
          <!-- 微信手机号获取按钮 -->
          <button
            class="wechat-phone-btn" :open-type="agreed ? 'getPhoneNumber' : ''" :disabled="loading"
            @getphonenumber="getPhoneNumber" @click="getPhoneNumber"
          >
            <text class="btn-text">同意授权手机号并继续</text>
          </button>

          <!-- 错误提示 -->
          <view v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </view>

          <!-- 协议同意区域 -->
          <view class="agreement-container">
            <label class="agreement-label">
              <checkbox-group @change="checkboxChange">
                <checkbox :checked="agreed" color="#3B82F6" style="transform:scale(0.7)" />我已阅读并同意
              </checkbox-group>
            </label>
            <view class="agreement-links">
              <text class="agreement-link" @click="GoToDeclaration('agreement')">用户协议</text>
              和
              <text class="agreement-link" @click="GoToDeclaration('privacyPolicy')">隐私政策</text>
            </view>
          </view>
        </view>
        <view class="mt-4 flex  w-full justify-center text-base">
          <text @click="skipBindPhone">暂不继续</text>
        </view>
      </view>

      <!-- 步骤2: 更新用户信息 -->
      <view v-if="currentStep === 2" class="step-content">
        <view class="header">
          <view class="title" />
          <view class="subtitle m-auto  inline-table text-left ">
            <view class="text-base font-bold">
              您的头像与昵称将用于:
            </view>
            <view class="text-base before:content-['·']">
              与智能助理互动
            </view>
            <view class="text-base before:content-['·']">
              解锁个性化记忆服务
            </view>
          </view>
        </view>

        <!-- 用户信息表单 -->
        <view class="user-info-form">
          <!-- 昵称输入 -->
          <view class="input-group m-auto w-64">
            <view class="input-container flex items-center overflow-hidden rounded-lg border border-gray-300 transition-all duration-200 focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-500">
              <view class="input-prefix flex items-center justify-center border-r border-gray-300 bg-blue-50 px-3 py-2.5 font-medium text-blue-600">
                昵称
              </view>
              <input
                v-model="userInfo.fullName"
                class="input-field flex-1 bg-white px-3 py-2.5 focus:outline-none"
                type="nickname"
                placeholder="请输入您的昵称"
                @change="updateUserInfo"
              >
            </view>
          </view>

          <!-- 头像上传 -->
          <view class="avatar-upload">
            <view class="avatar-preview">
              <image :src="userInfo.avatar || '/static/images/author.png'" class="avatar-image" />
            </view>
            <!-- #ifdef MP-WEIXIN -->
            <button open-type="chooseAvatar" class="avatar-btn" @chooseavatar="uploadWxAvatar">
              <uni-icons type="camera-filled" size="20" color="#fff" />
              <text class="avatar-btn-text">选择头像</text>
            </button>
            <!-- #endif -->

            <!-- #ifdef H5 -->
            <button class="avatar-btn" @click="uploadAvatar">
              <uni-icons type="camera-filled" size="20" />
              <text class="avatar-btn-text">选择头像</text>
            </button>
            <!-- #endif -->
          </view>
        </view>

        <view class="mt-4 flex  w-full justify-center text-base">
          <text @click="skipUserInfoUpdate">暂不继续</text>
        </view>
      </view>

      <!-- 装饰元素 -->
      <view class="decoration">
        <view class="decoration-circle-1" />
        <view class="decoration-circle-2" />
      </view>
    </view>
  </view>
</template>

<style scoped>
.bind-phone-container {
  @apply min-h-screen w-full flex flex-col items-center justify-center;
  background: linear-gradient(to bottom, #f0f9ff, #e0f2fe);
}

.bind-phone-content {
  @apply w-full max-w-md px-6 py-8 relative;
}

/* 步骤指示器样式 */
.steps-container {
  @apply mb-8;
}

.steps-progress {
  @apply relative mb-4;
}

.progress-bar {
  @apply h-1 bg-gray-200 rounded-full relative;
}

.progress-fill {
  @apply h-1 bg-blue-500 rounded-full absolute top-0 left-0 transition-all duration-300;
}

.steps-circles {
  @apply flex justify-between absolute w-full top-0 transform -translate-y-1/2;
}

.step-circle {
  @apply flex items-center justify-center w-6 h-6 rounded-full bg-white border border-gray-300 text-gray-500 transition-all duration-300;
}

.step-circle.active {
  @apply bg-blue-500 border-blue-500 text-white;
}

.step-circle.completed {
  @apply bg-green-500 border-green-500 text-white;
}

.step-number {
  @apply text-xs font-medium;
}

.steps-labels {
  @apply flex justify-between mt-2;
}

.step-label {
  @apply text-xs text-gray-500 transition-all duration-300;
}

.step-label.active {
  @apply text-blue-500 font-medium;
}

/* 步骤内容样式 */
.step-content {
  @apply w-full;
}

.header {
  @apply mb-8 text-center;
}

.title {
  @apply text-xl font-bold text-gray-800 mb-2;
}

.subtitle {
  @apply text-sm text-gray-600;
}

/* 步骤1: 绑定手机号样式 */
.bind-form {
  @apply flex flex-col items-center;
}

.wechat-phone-btn {
  @apply w-full py-3 rounded-lg bg-gradient-to-r from-green-400 to-green-600 text-white shadow-md transition duration-300 hover:shadow-lg mb-4;
}

.btn-text {
  @apply text-base font-medium;
}

.error-message {
  @apply text-red-500 text-sm mt-2 mb-4;
}

.agreement-container {
  @apply flex flex-wrap items-center justify-center mt-4;
}

.agreement-label {
  @apply text-sm flex items-center;
}

.agreement-links {
  @apply text-sm text-gray-600;
}

.agreement-link {
  @apply text-blue-500;
}

/* 步骤2: 用户信息更新样式 */
.user-info-form {
  @apply flex flex-col items-center;
}

.avatar-upload {
  @apply flex flex-col items-center mb-6;
}

.avatar-preview {
  @apply w-20 h-20 rounded-full overflow-hidden bg-gray-200 mb-3;
}

.avatar-image {
  @apply w-full h-full object-cover;
}

.avatar-btn {
  @apply flex items-center justify-center py-1 px-1 rounded-lg h-10 bg-blue-500 text-white border border-blue-200 after:border-none;
}

.avatar-btn-text {
  @apply text-sm ml-1;
}

.input-group {
  @apply mb-6;
}

.input-label {
  @apply block w-10 font-medium text-gray-700 py-2;
}

.input-field {
  @apply px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.buttons-container {
  @apply flex justify-between w-full mt-4;
}

.skip-btn {
  @apply py-2 px-6 rounded-lg border border-gray-300 text-gray-600 bg-white;
}

.submit-btn {
  @apply py-2 px-8 rounded-lg bg-blue-500 text-white shadow-md;
}

/* 装饰元素 */
.decoration {
  @apply absolute inset-0 overflow-hidden pointer-events-none;
  z-index: -1;
}

.decoration-circle-1 {
  @apply absolute rounded-full bg-blue-500 opacity-5;
  width: 300px;
  height: 300px;
  top: -100px;
  right: -100px;
}

.decoration-circle-2 {
  @apply absolute rounded-full bg-blue-500 opacity-5;
  width: 200px;
  height: 200px;
  bottom: 20%;
  left: -100px;
}
</style>
