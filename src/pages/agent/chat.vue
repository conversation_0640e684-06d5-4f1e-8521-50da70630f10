<script setup lang="ts">
import ChatTitle from '@/components/chat/ChatTitle.vue'
import ChatWorkflow from '@/components/chat/ChatWorkflow.vue'
import Drawer from '@/components/chat/Drawer.vue'
import MessageSender from '@/components/chat/MessageSender.vue'

import RechargeTips from '@/components/chat/rechargeTips.vue'
import RevMessage from '@/components/chat/revMessage.vue'
import UserMessage from '@/components/chat/userMessage.vue'
import { useChatStore } from '@/stores/chat'
import { useDifyChatStore } from '@/stores/dify-chat'
import { computed, nextTick, onMounted, ref, watch } from 'vue'

const props = defineProps({
  id: String,
  agentName: String,
  type: String,
  conversationId: String,
})
const scrollTop = ref(0)
const titleHeight = ref(0)
const sendHeight = ref(0)
// 抽屉状态管理
const drawerOpen = ref(false)
const animationState = ref('closed')
const scrollInterval = ref<ReturnType<typeof setInterval> | null>(null)
const newMessage = ref('')
const sendStatus = ref(false)
const chatStore = useChatStore()
const difyStore = useDifyChatStore()
const scrollViewRef = ref(null)
// 从 dify store 中解构出所需的方法
const {
  initDifyApp, // 初始化 Dify 应用
  setAppId, // 设置应用 ID
  setType, // 设置类型
  startNewConversation, // 开始新对话
  checkCanUse,

} = difyStore

const { rechargeTips, userInputFormList, sendMmoryStatus, conversation_id } = storeToRefs(difyStore)
// 滚动到底部
function scrollToBottom() {
  // 使用 nextTick 确保 DOM 已更新
  nextTick(() => {
    // #ifdef H5
    // H5环境下使用页面滚动
    uni.pageScrollTo({
      scrollTop: 99999,
      duration: 300,
    })
    // #endif

    // #ifdef MP-WEIXIN
    // 微信小程序环境下使用选择器查询获取scroll-view
    const query = uni.createSelectorQuery()
    query.select('.messages-wrapper').boundingClientRect(() => {})
    query.selectViewport().scrollOffset(() => {})
    query.exec((res) => {
      if (res[0] && res[1]) {
        // 使用scroll-view的scrollTop属性

        scrollTop.value = res[0].height
      }
      else {
        scrollTop.value = scrollTop.value + 20
      }
    })
    // #endif
  })
}
// 注意：自动滚动功能已移除，因为它依赖于未定义的变量
// 设置自动滚动
function setupAutoScroll() {
  // 清除已存在的定时器
  if (scrollInterval.value) {
    clearInterval(scrollInterval.value)
    scrollInterval.value = null
  }

  // 如果正在加载，设置定时滚动
  if (isLoadingMessage.value) {
    scrollInterval.value = setInterval(scrollToBottom, 500)
  }
}
// 关闭抽屉菜单
function closeDrawer() {
  animationState.value = 'closed'
  setTimeout(() => {
    drawerOpen.value = false
  }, 300) // 等待动画完成
}

// 从 store 中获取响应式状态
const {
  messages,
  isLoadingApp,
  isRequesting,
  isLoadingMessage, // 是否正在加载消息
  currentConversationId, // 当前会话 ID
  conversationHistory, // 会话历史列表
} = storeToRefs(difyStore)

// 计算当前会话标题
const currentChatTitle = computed(() => {
  if (!currentConversationId.value) {
    return props.agentName || 'AI聊天助手'
  }

  // 从会话历史中查找当前会话
  const currentChat = conversationHistory.value.find(chat => chat.id === conversation_id.value)
  return currentChat?.name || props.agentName || 'AI聊天助手'
})

// 监听消息变化，当加载新会话时滚动到底部
watch(isLoadingMessage, (newValue) => {
  console.log(newValue, 'newValuenewValuenewValuenewValue')

  if (newValue) {
    setupAutoScroll()
  }
  else {
    // 停止加载时清除定时器
    if (scrollInterval.value) {
      clearInterval(scrollInterval.value)
      scrollInterval.value = null
    }
    // 最后再滚动一次确保显示最新消息
    nextTick(() => {
      scrollToBottom()
    })
  }
})

function checkMemoryStatus() {
  if (userInputFormList.value.length > 0) {
    for (const item in userInputFormList.value) {
      if (userInputFormList.value[item].variable === 'memory') {
        sendMmoryStatus.value = true
        return
      }
      else {
        sendMmoryStatus.value = false
      }
    }
  }
  else {
    sendMmoryStatus.value = false
  }
}

// 分享相关数据
const shareParams = ref({
  title: '',
  path: '',
  imageUrl: '',
  desc: '',

})

// 发送消息
async function sendMessage(message: string) {
  if (!message.trim()) { return }

  // 设置发送状态
  sendStatus.value = true

  try {
    // 使用 difyStore 发送消息
    await difyStore.sendMessage(message)

    // 消息已发送，清空输入框
    newMessage.value = ''

    // 滚动到底部
    await nextTick()
    scrollToBottom()
  }
  catch (_error) {
    uni.showToast({
      title: '发送失败，请重试',
      icon: 'none',
    })
  }
  finally {
    // 重置发送状态
    sendStatus.value = false
  }
}

// 处理消息分享
function handleShareMessage(data: { content: string, shareType: string }) {
  console.log('dfdsfsdf')
  // 设置要分享的内容
}

// 处理建议问题点击
function handleQuestionSelect(question: string) {
  // 发送选中的建议问题
  sendMessage(question)
  // 清空建议问题列表
  difyStore.clearMessagesSuggested()
}

// 打开抽屉菜单
function openList() {
  drawerOpen.value = true
  setTimeout(() => {
    animationState.value = 'opened'
  }, 50)
}
function toggleDrawer() {
  if (drawerOpen.value) {
    closeDrawer()
  }
  else {
    openList()
  }
}

onShareAppMessage((_res) => {
  shareParams.value.title = `${props.agentName || '智能助手'}`
  shareParams.value.path = `/pages/agent/chat?id=${props.id}&agentName=${props.agentName}&conversationId=${currentConversationId.value}`
  return {
    title: shareParams.value.title || 'AI智能员工 一人可抵百人用', // 标题
    path: shareParams.value.path, // 分享路径
    imageUrl: 'https://ctutangg91hkparu99tg.baseapi.memfiredb.com/storage/v1/object/public/icon/icon/share11.png', // 分享图
    // desc: '小程序描述描述描述描述',
  }
})

onShareTimeline(() => {
  shareParams.value.title = `${props.agentName || '智能助手'}`
  shareParams.value.path = `/pages/agent/chat?id=${props.id}&agentName=${props.agentName}`
  return {
    title: shareParams.value.title || 'AI智能员工 一人可抵百人用', // 标题
    path: shareParams.value.path, // 分享路径
    imageUrl: 'https://ctutangg91hkparu99tg.baseapi.memfiredb.com/storage/v1/object/public/icon/icon/share11.png', // 分享图
  }
})

onLoad(async () => {
  // 可以在这里添加页面加载时的逻辑

  // 初始化逻辑
  // 设置默认分享路径
  if (props.id) {
    setAppId(props.id)
  }

  if (props.type) {
    setType(props.type as 'chat' | 'completion')
  }

  try {
    // 初始化 Dify 应用
    await initDifyApp()
    checkMemoryStatus()
    // 如果有消息，滚动到底部

    await checkCanUse()
    // if (!canUse) { return }
  }
  catch (_error) {
    uni.showToast({
      title: '初始化失败，请重试',
      icon: 'none',
    })
  }
})

function setSendHeight(int: number) {
  sendHeight.value = int
}

function setTitleHeight(int: number) {
  titleHeight.value = int
}

// 处理充值提示窗口关闭
function handleRechargeTipsClose() {
  rechargeTips.value = false
}

function handleNewChat() {
  startNewConversation()
}

// 组件卸载时清理定时器
onShow(() => {
  console.log(scrollInterval.value)

  if (scrollInterval.value) {
    clearInterval(scrollInterval.value)
    scrollInterval.value = null
  }
})
</script>

<template>
  <view class="chat-page">
    <!-- 主聊天区域 -->
    <view
      id="chatbox" class="chat-main-container"
      :style="{ transform: animationState === 'opened' ? 'scale(0.95) translateX(300px)' : '' }"
    >
      <!-- 聊天标题栏 -->
      <ChatTitle
        :title="currentChatTitle" @toggle-drawer="toggleDrawer" @setTitleHeight="setTitleHeight"
        @newChat="handleNewChat"
      />

      <!-- 聊天消息区域 -->
      <uni-load-more v-if="isLoadingApp" status="loading" />
      <scroll-view
        v-else id="chat-messages" ref="scrollViewRef" class="chat-messages-container" scroll-y="true"
        enable-back-to-top="true" scroll-with-animation="true" :scroll-top="scrollTop"
      >
        <view class="messages-wrapper">
          <!-- 消息列表 -->
          <template v-for="(message, index) in messages" :key="message.id">
            <ChatWorkflow v-if="message.workflows && message.workflows.length > 0" :nodes="message.workflows || []" />
            <!-- 用户消息 -->
            <UserMessage v-if="!message.isBot" :message="message.content" />
            <!-- AI消息 -->

            <RevMessage
              v-else :message="message.content" :isRequesting="isRequesting && index === messages.length - 1"
              :openingStatement="message.isOpeningStatement" @shareMessage="handleShareMessage"
            />
          </template>

          <!-- 底部空白区域，确保最后一条消息不被输入框遮挡 -->
          <view class="messages-bottom-space" />
        </view>
      </scroll-view>
      <RechargeTips v-if="rechargeTips" :visible="rechargeTips" :app-id="props.id" @close="handleRechargeTipsClose" />

      <!-- 消息输入区域 -->
      <MessageSender :inputFormList="userInputFormList" @send="sendMessage" @setHeight="setSendHeight" @selectQuestion="handleQuestionSelect" />
    </view>

    <!-- 抽屉菜单 -->
    <view
      id="leftInfo" class="drawer-container" :class="{
        'drawer-opened': drawerOpen && animationState === 'opened',
        'drawer-closed': !drawerOpen || animationState === 'closed',
      }"
    >
      <Drawer @closeDrawer="closeDrawer" @newChat="handleNewChat" />
    </view>

    <!-- 遮罩层 -->
    <view
      v-if="drawerOpen" class="drawer-overlay" :class="{ 'overlay-visible': animationState === 'opened' }"
      @click="closeDrawer"
    />
  </view>
</template>

<style scoped lang="scss">
@import url(./chat.scss);
</style>
