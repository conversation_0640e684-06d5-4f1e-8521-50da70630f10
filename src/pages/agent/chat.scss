/* 页面整体样式 */
.chat-page {
  @apply relative h-screen w-screen overflow-hidden;
  background: linear-gradient(to bottom, #f0f9ff, #e0f2fe);
}

/* 主聊天区域样式 */
.chat-main-container {
  @apply flex h-screen w-screen flex-col;
  will-change: transform;
  transform-origin: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: #f8fafc;
}

/* 聊天消息区域样式 */
.chat-messages-container {
  @apply flex-1 overflow-y-auto;
  scroll-behavior: smooth;
  background-color: #f8fafc;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.03) 1%, transparent 1%),
    radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.03) 1%, transparent 1%);
  background-size: 60px 60px;
}

/* 消息列表容器 */
.messages-wrapper {
  @apply flex flex-col space-y-4 p-4 pb-28;
}

/* 底部空白区域 */
.messages-bottom-space {
  @apply h-20;
}

/* 抽屉菜单样式 */
.drawer-container {
  @apply fixed left-0 top-0 z-50 h-full w-72 overflow-y-auto;
  will-change: transform;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  background-color: white;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 抽屉状态类 */
.drawer-opened {
  transform: translateX(0);
}

.drawer-closed {
  transform: translateX(-100%);
}

/* 遮罩层样式 */
.drawer-overlay {
  @apply fixed inset-0 z-40 bg-black;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.overlay-visible {
  opacity: 0.3;
  pointer-events: auto;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(59, 130, 246, 0.3);
  border-radius: 10px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}
