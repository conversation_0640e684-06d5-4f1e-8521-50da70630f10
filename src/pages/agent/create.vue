<script setup lang="ts">
import { API } from '@/api/api'
import { useAppStore } from '@/stores/app'
import { useCategoryStore } from '@/stores/category'
import { uploadStore } from '@/stores/upload'
import { useUsersStore } from '@/stores/user'
import { get } from '@/utils/http'
import { ref } from 'vue'

const upload = uploadStore()
const categoryStore = useCategoryStore()
// 表单状态
const isLoading = ref(false)
const formError = ref('')
const categoryList = ref()
const appStore = useAppStore()
const userStore = useUsersStore()
const operation = ref('创建')
const addOrUpdateAppId = ref('')
// 表单数据
const form = ref({
  companyId: '', // 当前公司id
  name: '', // 名称
  avatar: '', // 图标
  description: '', // 简介
  type: '', // 模型类型
  apiKey: '', // api-key
  company_user_points_rate: null, // 用户积分比率
  tags: '', // 标签
  categoryId: '', // 分类id

  // 收费模式相关 // 必传
  isFree: null, // 是否收费
  pictureRemark: '', //  二维码图片
  newUserTryTime: null, // 试用次数

})

// 智能体类型定义
const agentTypes = [
  { id: 'chat', name: 'chat', icon: 'chat', iconColor: 'text-blue-500' },
  { id: 'workflow', name: 'workflow', icon: 'staff', iconColor: 'text-purple-500' },
  { id: 'completion', name: 'completion', icon: 'paperplane', iconColor: 'text-orange-500' },
]

async function fetchUsers() {
  const result = await userStore.getCurrentUserInfo()
  console.log('获取公司', result)
  if (result) {
    // 获取当前公司id
    form.value.companyId = result.current_company_id
  }
}

// 验证表单
function validateForm() {
  if (!form.value.name.trim()) {
    formError.value = '请输入智能体名称'
    return false
  }

  if (!form.value.description.trim()) {
    formError.value = '请输入智能体简介'
    return false
  }

  if (!form.value.apiKey.trim()) {
    formError.value = '请输入API Key'
    return false
  }

  return true
}

onLoad(async (options) => {
  // 获取分类数据
  await getCategoryList()
  // 获取用户信息
  await fetchUsers()
  if (options && typeof options.appId !== 'undefined') {
    await getUpdateAppData(options.appId)
    console.log('dsfsdfsdf', options)
  }
})

// 获取编辑信息并且填充上去
async function getUpdateAppData(appId: string) {
  // 填充信息数据
  const appDetail = await appStore.getAppById(appId)
  console.log('编辑应用信息', appDetail)

  form.value.name = appDetail.name
  form.value.avatar = appDetail.icon
  form.value.description = appDetail.description
  form.value.tags = appDetail.tags.join(',')
  form.value.apiKey = appDetail.apikey
  form.value.type = appDetail.model_type
  form.value.categoryId = appDetail.category_id
  form.value.company_user_points_rate = appDetail.company_user_points_rate
  form.value.isFree = appDetail.is_free
  form.value.pictureRemark = appDetail.picture_remark
  form.value.newUserTryTime = appDetail.new_user_try_time
  addOrUpdateAppId.value = appId
  // 修改对应的文案
  operation.value = '编辑'
}

async function getCategoryList() {
  const result = await categoryStore.getAppCategories(1, 100)
  if (result.count > 0) {
    categoryList.value = result.data
  }
}

function handleCategoryChange(e) {
  const index = e.detail.value
  form.value.categoryId = categoryList.value[index].id
}

// 创建/编辑智能体
async function createAgent() {
  console.log('接受参数', form.value)
  console.log('createAgent 函数被调用')
  // 验证表单
  if (!validateForm()) {
    uni.showToast({
      title: formError.value,
      icon: 'none',
      duration: 2000,
    })
    return
  }

  try {
    // 验证API
    console.log('zhinengti返回', 'dd')
    const response = await get(
      API.dify.check,
      {
        appkey: form.value.apiKey,
      },
    )
    isLoading.value = true
    if (operation.value === '创建') {
      const result = await appStore.createApp(
        form.value.companyId,
        form.value.name,
        form.value.avatar,
        form.value.description,
        form.value.tags.split(','),
        form.value.apiKey,
        form.value.type,
        form.value.categoryId,
        form.value.company_user_points_rate,
      )
      addOrUpdateAppId.value = result.id
    }
    else {
    // 编辑智能体
      await appStore.updateApp(
        addOrUpdateAppId.value,
        form.value.name,
        form.value.avatar,
        form.value.description,
        form.value.tags.split(','),
        form.value.apiKey,
        form.value.type,
        form.value.categoryId,
        form.value.company_user_points_rate,
      )
      // 编辑智能体
      console.log('编辑智能体了噢')
    }
    console.log('成功1了')
    // 修改收费模式
    await appStore.updateCompanyApp(
      form.value.companyId,
      addOrUpdateAppId.value,
      form.value.isFree,
      form.value.pictureRemark,
      form.value.newUserTryTime,
    )
    console.log('成功3了')
    // 显示成功提示
    uni.hideLoading()
    uni.showToast({
      title: '智能体创建成功',
      icon: 'success',
      duration: 2000,
    })
    console.log('成功4了')
    // 创建成功后跳转到首页
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index',
      })
    }, 2000)
  }
  catch (_error) {
    let errorMessage = '创建失败!，请重试'
    uni.hideLoading()
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000,
    })
  }
  finally {
    isLoading.value = false
  }
}

// 新增：选择头像的方法
function chooseAvatar(type: number = 1) {
  uni.chooseImage({
    count: 1, // 只选一张图片
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'], // 可以从相册或相机选择
    success: async (res) => {
      const iconUrl = await upload.uploadIcon(res)
      if (type === 1) {
        form.value.avatar = iconUrl
      }
      else {
        form.value.pictureRemark = iconUrl
      }
    },
    fail: (err) => {
      console.error('选择头像失败', err)
    },
  })
}
</script>

<template>
  <view class="create-agent-page">
    <!-- 页面背景装饰 -->
    <view class="page-decoration">
      <view class="decoration-circle-1" />
      <view class="decoration-circle-2" />
    </view>

    <!-- 表单内容区 -->
    <view class="form-container">
      <view class="form-header">
        <view class="form-title">
          {{ operation }}您的智能助手
        </view>
        <!-- <view class="form-subtitle">自定义一个完全符合你需求的AI助手</view> -->
      </view>

      <!-- 基本信息区域 -->
      <view class="form-section">
        <view class="section-title">
          基本信息
        </view>
        <!-- 添加选择图片的按钮和预览区域 -->
        <view class="form-group">
          <label class="form-label">智能体图标</label>
          <view>
            <button class="avatar-uploader" @click="chooseAvatar(1)">
              <view v-if="!form.avatar" class="upload-placeholder">
                <uni-icons type="camera" size="20" color="#3B82F6" />
                <text class="upload-text">选择图标</text>
              </view>
              <view v-if="form.avatar" class="avatar-preview">
                <image :src="form.avatar" class="size-full object-cover" />
              </view>
            </button>
          </view>
        </view>

        <view class="form-group">
          <label class="form-label">智能体名称</label>
          <view class="input-container">
            <uni-icons type="chat" size="18" color="#3B82F6" class="input-icon" />
            <input v-model="form.name" class="form-input" placeholder="请输入智能体名称">
          </view>
        </view>
        <view class="form-group">
          <label class="form-label">智能体标签</label>
          <view class="input-container">
            <uni-icons type="chat" size="18" color="#3B82F6" class="input-icon" />
            <input v-model="form.tags" class="form-input" placeholder="请输入智能体标签 以逗号分隔">
          </view>
        </view>

        <view class="form-group">
          <label class="form-label">智能体简介</label>
          <view class="input-container textarea-container">
            <uni-icons type="chat" size="18" color="#3B82F6" class="input-icon input-icon-top" />
            <textarea v-model="form.description" class="form-textarea" placeholder="请输入智能体简介" />
          </view>
        </view>
        <view class="form-group">
          <label class="form-label">用户积分比率</label>
          <view class="input-container">
            <uni-icons type="chat" size="18" color="#3B82F6" class="input-icon" />
            <input v-model="form.company_user_points_rate" class="form-input" placeholder="请输入为数字的用户积分比率">
          </view>
        </view>

        <view class="form-group">
          <label class="form-label">选择分类</label>
          <view class="input-container">
            <uni-icons type="list" size="18" color="#3B82F6" class="input-icon" />
            <picker
              mode="selector" :range="categoryList" range-key="title" class="category-picker"
              @change="handleCategoryChange"
            >
              <view class="picker-content">
                {{ form.categoryId ? categoryList.find(c => c.id === form.categoryId)?.title : '请选择分类' }}
              </view>
            </picker>
            <uni-icons type="arrowdown" size="16" color="#9CA3AF" class="picker-arrow" />
          </view>
        </view>
      </view>

      <!-- 类型选择区域 -->
      <view class="form-section">
        <view class="section-title">
          选择模型类型
        </view>
        <view class="type-selector">
          <view
            v-for="type in agentTypes" :key="type.id" class="type-option"
            :class="{ selected: form.type === type.id }" @click="form.type = type.id"
          >
            <view class="type-icon-container" :class="type.iconColor.replace('text-', 'bg-').replace('-500', '-100')">
              <uni-icons
                :type="type.icon" size="20"
                :color="type.iconColor.replace('text-', '#').replace('blue-500', '3B82F6').replace('purple-500', '8B5CF6').replace('green-500', '10B981')"
              />
            </view>
            <view class="type-content">
              <view class="type-name">
                {{ type.name }}
              </view>
            </view>
            <view v-if="form.type === type.id" class="type-selected-mark">
              <uni-icons type="checkmarkempty" size="16" color="#FFFFFF" />
            </view>
          </view>
        </view>
      </view>

      <!-- API设置区域 -->
      <view class="form-section">
        <view class="section-title">
          API设置
        </view>

        <view class="form-group">
          <label class="form-label">API Key</label>
          <view class="input-container">
            <uni-icons type="key" size="18" color="#3B82F6" class="input-icon" />
            <input v-model="form.apiKey" class="form-input" placeholder="请输入API Key" type="password">
          </view>
        </view>
      </view>

      <view class="form-section">
        <view class="section-title">
          收费模式设置
        </view>

        <view class="form-group">
          <label class="form-label">是否使用付费</label>
          <view class="radio-group">
            <label class="radio-option">
              <radio :checked="form.isFree === true" color="#3B82F6" @click="form.isFree = true" />
              <text class="radio-label">是</text>
            </label>
            <label class="radio-option">
              <radio :checked="form.isFree === false" color="#3B82F6" @click="form.isFree = false" />
              <text class="radio-label">否</text>
            </label>
          </view>
        </view>
        <view class="form-group">
          <label class="form-label">上传二维码</label>
          <view>
            <button class="wx-uploader" @click="chooseAvatar(2)">
              <view v-if="!form.pictureRemark" class="upload-placeholder">
                <uni-icons type="camera" size="20" color="#3B82F6" />
                <text class="upload-text">上传二维码</text>
              </view>
              <view v-if="form.pictureRemark" class="avatar-preview">
                <image :src="form.pictureRemark" class="size-full object-cover" />
              </view>
            </button>
          </view>
        </view>
        <view class="form-group">
          <label class="form-label">用户试用次数</label>
          <view class="input-container">
            <uni-icons type="chat" size="18" color="#3B82F6" class="input-icon" />
            <input v-model="form.newUserTryTime" class="form-input" placeholder="请输入用户试用次数">
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮区域 -->
    <view class="submit-button-container">
      <button class="submit-button" @click="createAgent">
        <uni-icons type="plusempty" size="18" color="#FFFFFF" />
        <text class="button-text">{{ operation }}智能体</text>
      </button>
    </view>
  </view>
</template>

<style scoped>
/**单选框样式 */
.radio-group {
  display: flex;
  align-items: center;
}

.radio-option {
  display: flex;
  align-items: center;
  margin-right: 100px;
  /* 替代 space-x-4 */
}

.radio-label {
  margin-left: 8px;
  color: #4b5563;
}

/* 页面整体样式 */
.create-agent-page {
  @apply pb-24 min-h-screen relative;
  background: linear-gradient(to bottom, #f0f9ff, #e0f2fe);
}

/* 装饰元素 */
.page-decoration {
  @apply absolute inset-0 overflow-hidden pointer-events-none;
  z-index: 0;
}

.decoration-circle-1 {
  @apply absolute rounded-full bg-blue-500 opacity-5;
  width: 300px;
  height: 300px;
  top: -100px;
  right: -100px;
}

.decoration-circle-2 {
  @apply absolute rounded-full bg-blue-500 opacity-5;
  width: 200px;
  height: 200px;
  bottom: 20%;
  left: -100px;
}

/* 表单容器 */
.form-container {
  @apply p-5 relative z-10;
}

/* 表单头部 */
.form-header {
  @apply mb-6 text-center;
}

.form-title {
  @apply text-xl font-bold text-gray-800 mb-1;
}

.form-subtitle {
  @apply text-sm text-gray-500;
}

/* 表单分区 */
.form-section {
  @apply mb-8 bg-white rounded-xl p-5 shadow-sm;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.section-title {
  @apply text-base font-medium text-gray-800 mb-4 flex items-center;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: '';
  @apply absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-5 bg-blue-500 rounded-full;
}

/* 表单组件 */
.form-group {
  @apply mb-5;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* 输入框容器 */
.input-container {
  @apply flex items-center w-full px-4 py-2 border border-gray-200 rounded-lg bg-gray-50;
  transition: all 0.2s ease;
}

.input-container:focus-within {
  @apply border-blue-500 bg-white;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.input-icon {
  @apply mr-2;
}

.input-icon-top {
  @apply self-start mt-2;
}

.form-input {
  @apply w-full py-2 bg-transparent focus:outline-none text-gray-800;
}

.textarea-container {
  @apply items-start;
}

.form-textarea {
  @apply w-full py-2 bg-transparent focus:outline-none text-gray-800 h-24;
}

/* 头像上传器 */
.avatar-uploader {
  @apply w-24 h-24 rounded-full overflow-hidden bg-blue-50 flex items-center justify-center after:border-none;
  /* border: 2px dashed rgba(59, 130, 246, 0.3); */
  transition: all 0.2s ease;
}

.avatar-uploader:active {
  @apply bg-blue-100;
  transform: scale(0.98);
}

/** 二维码上传 */
.wx-uploader {
  @apply w-24 h-24 overflow-hidden bg-blue-50 flex items-center justify-center;
  /* border: 2px dashed rgba(59, 130, 246, 0.3); */
  transition: all 0.2s ease;
  /* 移除 rounded-full */
  /* 可以添加圆角，若需要圆角正方形 */
  border-radius: 4px;
}

.avatar-preview {
  @apply w-full h-full object-cover;
}

.upload-placeholder {
  @apply flex flex-col items-center justify-center;
}

.upload-text {
  @apply text-xs text-blue-500 font-medium mt-2;
}

/* 类型选择器 */
.type-selector {
  @apply flex flex-col space-y-3;
}

.type-option {
  @apply flex items-center p-2 border border-gray-200 rounded-lg relative;
  transition: all 0.2s ease;
}

.type-option:active {
  transform: scale(0.99);
}

.type-option.selected {
  @apply border-blue-500 bg-blue-50;
}

.type-icon-container {
  @apply flex items-center justify-center w-10 h-10 rounded-full mr-3;
}

.type-content {
  @apply flex-1;
}

.type-name {
  @apply text-sm font-medium text-gray-800 mb-1;
}

.type-description {
  @apply text-xs text-gray-500;
}

.type-selected-mark {
  @apply absolute top-3 right-3 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center;
}

/* 知识库上传器 */
.knowledge-uploader {
  @apply w-full p-4 border border-dashed border-blue-300 rounded-lg flex items-center bg-blue-50 mb-3;
  transition: all 0.2s ease;
}

.knowledge-uploader:active {
  @apply bg-blue-100;
}

.knowledge-icon-container {
  @apply mr-3;
}

.knowledge-content {
  @apply flex-1;
}

.upload-hint {
  @apply text-xs text-gray-400 mt-1 block;
}

/* 已上传文件列表 */
.knowledge-files {
  @apply mt-4 bg-gray-50 rounded-lg overflow-hidden;
}

.knowledge-file-item {
  @apply flex items-center p-3 border-b border-gray-100;
}

.knowledge-file-item:last-child {
  @apply border-b-0;
}

.file-icon {
  @apply mr-2;
}

.file-name {
  @apply text-sm text-gray-700 truncate flex-1;
}

/* 设置项 */
.settings-container {
  @apply bg-gray-50 rounded-lg overflow-hidden;
}

.toggle-item {
  @apply flex items-center justify-between py-3 px-4 border-b border-gray-100;
}

.toggle-item:last-child {
  @apply border-b-0;
}

.toggle-info {
  @apply flex items-center;
}

.toggle-label {
  @apply text-sm text-gray-700 ml-2;
}

/* 提交按钮 */
.submit-button-container {
  @apply p-10;
}

.submit-button {
  @apply w-full py-3 flex items-center justify-center space-x-2 rounded-lg text-white font-medium;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  transition: all 0.2s ease;
}

.submit-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px -1px rgba(59, 130, 246, 0.2);
}

.button-text {
  @apply ml-2;
}

.category-picker {
  @apply flex-1 py-2 text-gray-800;
}

.picker-content {
  @apply w-full;
}

.picker-arrow {
  @apply ml-2;
}

/* 未选择时的提示文字样式 */
.picker-content:empty:before {
  content: '请选择数据';
  @apply text-gray-400;
}
</style>
