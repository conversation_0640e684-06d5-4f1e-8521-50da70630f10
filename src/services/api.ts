/**
 * API Service
 *
 * This file demonstrates how to use the request utility with a baseUrl configuration.
 * It creates a configured instance of the request utility and exports it for use in the application.
 */
import type { HttpRequest } from '@/utils/http'
import { createRequest } from '@/utils/http'

// Create a configured instance of the request utility
const apiRequest: HttpRequest = createRequest({
  baseURL: import.meta.env.VITE_API_BASE || 'https://api.example.com',
  timeout: 30000, // 30 seconds
  header: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
})

// Request interceptor example: Add authentication token to requests
apiRequest.interceptors.request.use((config) => {
  // Get token from storage
  const token = uni.getStorageSync('token')

  // If token exists, add it to the request headers
  if (token) {
    config.header = {
      ...config.header,
      Authorization: `Bearer ${token}`,
    }
  }

  return config
})

// Response interceptor example: Handle common response scenarios
apiRequest.interceptors.response.use(
  (response) => {
    // Check if the response has the expected structure
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return response.data
    }

    // Handle specific error codes
    if (response.statusCode === 401) {
      // Unauthorized, clear token and redirect to login
      uni.removeStorageSync('token')
      uni.navigateTo({ url: '/pages/auth/login' })
      return Promise.reject(new Error('Unauthorized'))
    }

    // Handle other error scenarios
    return Promise.reject(new Error(response.data?.message || 'Request failed'))
  },
  (error) => {
    // Handle network errors or request cancellations
    const errorMessage = error.errMsg || 'Network error'

    // Show error message to user
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000,
    })

    return Promise.reject(error)
  },
)

export default apiRequest
