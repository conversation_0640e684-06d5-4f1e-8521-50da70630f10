/**
 * API Configuration
 * 
 * This file contains the configuration for the API service.
 * It defines the base URL and other settings for the API.
 */

// Environment-specific configuration
const env = {
  development: {
    baseURL: 'http://localhost:3000', // Development server
  },
  production: {
    baseURL: 'https://api.example.com', // Production server
  },
  test: {
    baseURL: 'https://test-api.example.com', // Test server
  },
};

// Determine current environment
// In a real application, you might want to use import.meta.env or process.env
const currentEnv = process.env.NODE_ENV || 'development';

// Default configuration
export const apiConfig = {
  // Base URL for API requests
  baseURL: env[currentEnv]?.baseURL || 'https://api.example.com',
  
  // Default timeout in milliseconds
  timeout: 30000,
  
  // Default headers
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  
  // Whether to verify SSL certificates
  sslVerify: true,
  
  // Whether to send cookies with cross-origin requests
  withCredentials: false,
};

export default apiConfig;
