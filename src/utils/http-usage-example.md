# HTTP 工具使用说明

## 网络异常处理功能

本 HTTP 工具已增强了对 `ERR_NETWORK_CHANGED` 网络异常的处理能力，包括自动重试机制和用户友好的错误提示。

## 主要功能

### 1. 自动重试机制
- 检测到 `ERR_NETWORK_CHANGED` 错误时自动重试
- 使用指数退避策略（1秒、2秒、4秒...）
- 默认最大重试 3 次
- 重试前检查网络连接状态

### 2. 智能错误处理
- 区分网络变更错误和其他网络错误
- 提供不同的用户提示信息
- 检查网络可用性

### 3. 配置化重试参数
- 可自定义最大重试次数
- 可自定义重试延迟时间

## 使用方法

### 基本使用（使用默认重试配置）

```javascript
import { get, post, put, del } from '@/utils/http'

// GET 请求
const data = await get('/api/users')

// POST 请求
const result = await post('/api/users', { name: '<PERSON>' })
```

### 自定义重试配置

```javascript
import { get, post } from '@/utils/http'

// 自定义重试参数
const retryConfig = {
  maxRetries: 5,        // 最大重试 5 次
  retryDelay: 2000      // 初始延迟 2 秒
}

const data = await get('/api/users', {}, false, {}, retryConfig)
const result = await post('/api/users', { name: 'John' }, {}, retryConfig)
```

### 使用创建的请求实例

```javascript
import { createRequest } from '@/utils/http'

const httpClient = createRequest({
  maxRetries: 3,
  retryDelay: 1500,
  header: {
    'Custom-Header': 'value'
  }
})

const data = await httpClient.get('/api/users')
```

### 流式请求（AI 对话等场景）

```javascript
import { streamRequest } from '@/utils/http'

const requestTask = streamRequest(
  '/api/chat/stream',
  'POST',
  { 'Content-Type': 'application/json' },
  { message: 'Hello' },
  (chunk) => {
    // 处理数据块
    console.log('收到数据块:', chunk)
  },
  (response) => {
    // 请求完成
    console.log('请求完成:', response)
  }
)

// 可以中断请求
// requestTask.abort()
```

## 错误处理说明

### 网络变更错误 (ERR_NETWORK_CHANGED)
- 自动检测并重试
- 显示"网络环境变更，正在重试..."提示
- 重试失败后显示"网络环境多次变更，请稍后重试"

### 网络不可用
- 检测到网络不可用时显示"网络连接不可用，请检查网络设置"
- 不进行重试

### 其他网络错误
- 显示通用"网络错误"提示
- 不进行重试

## 导出的工具函数

```javascript
import { checkNetworkStatus, isNetworkChangeError } from '@/utils/http'

// 检查网络状态
const isOnline = await checkNetworkStatus()

// 检查是否为网络变更错误
const isNetworkChange = isNetworkChangeError(error)
```

## 注意事项

1. 重试机制仅对 `ERR_NETWORK_CHANGED` 错误生效
2. 重试前会检查网络连接状态，无网络时不会重试
3. 使用指数退避策略避免频繁请求
4. 流式请求不支持自动重试，需要手动重新发起
5. 重试配置是可选的，不传入时使用默认值
