// utils/http.ts
const BASE_URL = import.meta.env.VITE_API_BASE

// 类型定义
export interface RequestOptions {
  baseURL?: string
  timeout?: number
  header?: Record<string, string>
  maxRetries?: number
  retryDelay?: number
}

// 重试配置
interface RetryConfig {
  maxRetries: number
  retryDelay: number
  currentRetry: number
}

// 网络错误类型检测
function isNetworkChangeError(error: any): boolean {
  if (!error) { return false }

  const errorMsg = error.errMsg || error.message || ''
  return errorMsg.includes('ERR_NETWORK_CHANGED')
    || errorMsg.includes('network changed')
    || errorMsg.includes('网络已更改')
}

// 检查网络连接状态
function checkNetworkStatus(): Promise<boolean> {
  return new Promise((resolve) => {
    uni.getNetworkType({
      success: (res) => {
        resolve(res.networkType !== 'none')
      },
      fail: () => {
        resolve(false)
      },
    })
  })
}

// 延迟函数
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export interface HttpRequest {
  get: (url: string, params?: any, cache?: boolean, headers?: Record<string, string>) => Promise<any>
  post: (url: string, data?: any, headers?: Record<string, string>) => Promise<any>
  put: (url: string, data?: any, headers?: Record<string, string>) => Promise<any>
  del: (url: string, data?: any, headers?: Record<string, string>) => Promise<any>
  streamRequest: (url: string, method: string, data?: any, onChunk?: (chunk: any) => void, onComplete?: (data: any) => void, headers?: Record<string, string>) => UniApp.RequestTask
}

// 通用请求方法封装（带重试功能）
function request(url: string, method: string, data: any = {}, cache = false, headers: Record<string, string> = {}, retryConfig?: Partial<RetryConfig>) {
  const config: RetryConfig = {
    maxRetries: retryConfig?.maxRetries ?? 3,
    retryDelay: retryConfig?.retryDelay ?? 1000,
    currentRetry: retryConfig?.currentRetry ?? 0,
  }

  return new Promise((resolve, reject) => {
    const wxAuth = uni.getStorageSync('wxAuth') || ''
    const token = uni.getStorageSync('wxAuth') || ''

    // 缓存处理
    const cacheKey = `${method}:${url}:${JSON.stringify(data)}`
    if (cache) {
      const cachedData = uni.getStorageSync(cacheKey)
      if (cachedData) {
        // 在非生产环境下输出缓存信息
        // console.log('返回缓存数据：', cacheKey)
        return resolve(cachedData)
      }
    }

    const makeRequest = async () => {
      uni.request({
        url: BASE_URL + url,
        method: method as any,
        // #ifdef web || h5
        withCredentials: true,
        // #endif
        data,
        header: {
          'Content-Type': 'application/json',
          'Authorization': token.replace('sb-ctutangg91hkparu99tg-auth-token=base64-', 'Bearer '),
          'wx-auth': wxAuth,
          ...headers,
        },
        success: (res) => {
          if (res.statusCode === 200) {
            if (cache) {
              uni.setStorageSync(cacheKey, res.data)
            }
            resolve(res.data)
          }
          else if (res.statusCode === 401) {
            uni.showToast({ title: '请重新登录', icon: 'none' })
            uni.reLaunch({ url: '/pages/auth/login' })
            reject(new Error('未登录'))
          }
          else if (res.statusCode === 500) {
            const errorMsg = typeof res.data === 'object' && res.data !== null && 'message' in res.data
              ? (res.data as any).message
              : '服务器错误'
            uni.showToast({ title: errorMsg, icon: 'none' })
            reject(new Error(errorMsg))
          }
          else {
            const errorMsg = typeof res.data === 'object' && res.data !== null && 'msg' in res.data ? res.data.msg : '请求失败'
            uni.showToast({ title: errorMsg, icon: 'none' })
            reject(new Error(errorMsg))
          }
        },
        fail: async (err) => {
          // 检查是否为网络变更错误
          if (isNetworkChangeError(err) && config.currentRetry < config.maxRetries) {
            console.log(`网络变更检测到，准备重试 ${config.currentRetry + 1}/${config.maxRetries}`)

            // 检查网络状态
            const isNetworkAvailable = await checkNetworkStatus()
            if (!isNetworkAvailable) {
              uni.showToast({ title: '网络连接不可用，请检查网络设置', icon: 'none' })
              reject(new Error('网络连接不可用'))
              return
            }

            // 显示重试提示
            uni.showToast({
              title: `网络环境变更，正在重试...`,
              icon: 'loading',
              duration: 1000,
            })

            // 等待一段时间后重试，使用指数退避
            const retryDelay = config.retryDelay * 2 ** config.currentRetry
            await delay(retryDelay)

            // 递归重试
            config.currentRetry++
            try {
              const result = await request(url, method, data, cache, headers, config)
              resolve(result)
            }
            catch (retryError) {
              reject(retryError)
            }
          }
          else {
            // 非网络变更错误或已达到最大重试次数
            const errorMsg = isNetworkChangeError(err)
              ? '网络环境多次变更，请稍后重试'
              : '网络错误'
            uni.showToast({ title: errorMsg, icon: 'none' })
            reject(err)
          }
        },
      })
    }

    makeRequest()
  })
}

/**
 * 流式请求方法 - 用于AI对话等需要流式响应的场景
 * @param url 请求地址
 * @param method 请求方法
 * @param headers 请求头
 * @param data 请求数据
 * @param onChunk 数据块回调函数
 * @param onComplete 请求完成回调函数

 * @returns Promise
 */
function streamRequest(urls: string, method: string, headers: Record<string, string> = {}, data: any = {}, onChunk?: (chunk: any) => void, onComplete?: (data: any) => void) {
  const wxAuth = uni.getStorageSync('wxAuth') || ''
  const token = uni.getStorageSync('token') || ''

  // 构建请求头
  const requestHeader = {
    'Authorization': token,
    'wx-auth': wxAuth,
    ...headers,
  }
  // 使用 uni.request 发起请求
  const requestTask = uni.request({
    url: BASE_URL + urls,
    method: method as any,
    data: method !== 'GET' ? data : {},
    header: requestHeader,
    // 响应类型设置为文本，便于处理流式数据
    responseType: 'arraybuffer',
    // 启用分块接收，实现真正的流式处理
    enableChunked: true,
    success: (res) => {
      console.log(res, 'success')
      if (onChunk) { onChunk(res) }

      // 处理成功响应
      if (res.statusCode === 200) {
        // 处理最后可能剩余的数据

        // 调用完成回调
      }
      else if (res.statusCode === 401) {
        uni.showToast({ title: '请重新登录', icon: 'none' })
        uni.reLaunch({ url: '/pages/auth/login' })
      }
      else {
        // 处理其他错误状态码
        const errorMsg = typeof res.data === 'object' && res.data !== null && 'msg' in res.data ? res.data.msg : '请求失败'
        uni.showToast({ title: errorMsg, icon: 'none' })
      }
    },
    complete: (res) => {
      console.log(res, 'complete')

      if (onComplete) { onComplete(res) }
    },
    fail: async (res) => {
      console.log(res, 'fail')

      // 检查是否为网络变更错误
      if (isNetworkChangeError(res)) {
        // 检查网络状态
        const isNetworkAvailable = await checkNetworkStatus()
        if (!isNetworkAvailable) {
          uni.showToast({ title: '网络连接不可用，请检查网络设置', icon: 'none' })
        }
        else {
          uni.showToast({ title: '网络环境变更，请重新发起请求', icon: 'none' })
        }
      }
      else {
        uni.showToast({ title: '网络错误', icon: 'none' })
      }
    },
  })

  // 返回请求任务，可用于中断请求
  return requestTask
}

/**
 * 创建配置化的请求实例
 * @param options 配置选项
 * @returns 请求实例
 */
export function createRequest(options: RequestOptions = {}): HttpRequest {
  // 使用配置的baseURL或默认值
  // 注意: 当前实现中没有使用该变量，如果需要使用请修改下面的代码
  // const baseURL = options.baseURL || BASE_URL
  const defaultHeaders = options.header || {}

  // 创建自定义请求函数
  const customRequest = (url: string, method: string, data: any = {}, cache = false, headers: Record<string, string> = {}) => {
    return request(url, method, data, cache, { ...defaultHeaders, ...headers })
  }

  // 创建自定义流式请求函数
  const customStreamRequest = (url: string, method: string, data: any = {}, onChunk?: (chunk: any) => void, onComplete?: (data: any) => void, headers: Record<string, string> = {}) => {
    return streamRequest(url, method, { ...defaultHeaders, ...headers }, data, onChunk, onComplete)
  }

  return {
    get: (url, params = {}, cache = false, headers = {}) => customRequest(url, 'GET', params, cache, headers),
    post: (url, data = {}, headers = {}) => customRequest(url, 'POST', data, false, headers),
    put: (url, data = {}, headers = {}) => customRequest(url, 'PUT', data, false, headers),
    del: (url, data = {}, headers = {}) => customRequest(url, 'DELETE', data, false, headers),
    streamRequest: customStreamRequest,
  }
}

// 导出 GET / POST / PUT / DELETE 方法（带重试功能）
export function get(url: string, params = {}, cache = false, headers = {}, retryConfig?: Partial<RetryConfig>) {
  return request(url, 'GET', params, cache, headers, retryConfig)
}
export function post(url: string, data = {}, headers = {}, retryConfig?: Partial<RetryConfig>) {
  return request(url, 'POST', data, false, headers, retryConfig)
}
export function put(url: string, data = {}, headers = {}, retryConfig?: Partial<RetryConfig>) {
  return request(url, 'PUT', data, false, headers, retryConfig)
}
export function del(url: string, data = {}, headers = {}, retryConfig?: Partial<RetryConfig>) {
  return request(url, 'DELETE', data, false, headers, retryConfig)
}

// 导出原始请求方法（向后兼容）
export const uniRequest = request

// 导出流式请求方法
export { streamRequest }

// 导出网络状态检查函数
export { checkNetworkStatus, isNetworkChangeError }
