{"compilerOptions": {"target": "esnext", "jsx": "preserve", "lib": ["esnext", "dom"], "useDefineForClassFields": true, "baseUrl": ".", "rootDir": ".", "module": "esnext", "moduleResolution": "node", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "types": ["@dcloudio/types"], "allowJs": true, "strict": true, "declaration": true, "outDir": "dist", "sourceMap": true, "esModuleInterop": true}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"]}