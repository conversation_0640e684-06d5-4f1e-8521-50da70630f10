#!/usr/bin/env python3
import subprocess
import sys
import io
import os
import csv
from datetime import datetime, timedelta

# 调试模式标志
DEBUG = True

def log(message, level="INFO"):
    """记录调试日志"""
    if DEBUG:
        print(f"[{level}] {message}", file=sys.stderr)

def set_utf8_output():
    """确保控制台输出使用UTF-8编码"""
    try:
        log("尝试设置UTF-8输出编码")
        if sys.platform.startswith('win'):
            import ctypes
            kernel32 = ctypes.windll.kernel32
            kernel32.SetConsoleOutputCP(65001)
            log("已为Windows系统设置控制台输出编码为UTF-8")

        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
        log("已设置标准输出/错误输出编码为UTF-8")
    except Exception as e:
        log(f"设置UTF-8输出失败: {e}", "ERROR")

def parse_date(date_str):
    """解析日期字符串为datetime对象"""
    return datetime.strptime(date_str, '%Y-%m-%d %H:%M')

def get_day_key(date_obj):
    """根据6点分割规则确定日期键"""
    # 如果时间在6点之前，属于前一天
    if date_obj.hour < 6:
        return (date_obj - timedelta(days=1)).strftime('%Y-%m-%d')
    return date_obj.strftime('%Y-%m-%d')

def get_git_commits(author):
    """获取指定作者的非merge提交记录并统计修改信息"""
    log(f"开始获取作者 '{author}' 的非merge提交记录")
    try:
        if not os.path.exists(".git"):
            log("当前目录不是git仓库", "ERROR")
            raise Exception("当前目录不是git仓库")

        commit_cmd = [
            "git", "log",
            f"--author={author}",
            "--no-merges",
            "--pretty=format:%H|%ad|%an|%s",
            "--date=format:%Y-%m-%d %H:%M",
        ]
        log(f"执行命令: {' '.join(commit_cmd)}")

        commit_result = subprocess.run(
            commit_cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            errors='replace',
            check=True,
        )

        commit_lines = commit_result.stdout.strip().split("\n")
        log(f"获取到 {len(commit_lines)} 条提交记录")

        commits = []
        for line in commit_lines:
            if not line:
                continue

            parts = line.split("|", 3)
            if len(parts) != 4:
                log(f"跳过格式不正确的提交记录: {line}", "WARNING")
                continue

            commit_hash, date_str, author, message = parts
            date_obj = parse_date(date_str)

            # 获取修改行数统计
            stats_cmd = [
                "git", "diff", "--shortstat",
                f"{commit_hash}~1", commit_hash,
            ]
            stats_result = subprocess.run(
                stats_cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='replace',
                check=False,
            )

            insertions = 0
            deletions = 0
            if stats_result.returncode == 0 and stats_result.stdout:
                stats = stats_result.stdout.strip()
                for part in stats.split(","):
                    part = part.strip()
                    if "insertions" in part:
                        insertions = int(part.split()[0])
                    elif "deletions" in part:
                        deletions = int(part.split()[0])

            total = insertions + deletions
            day_key = get_day_key(date_obj)

            commit = {
                "hash": commit_hash,
                "date_str": date_str,
                "date_obj": date_obj,
                "author": author,
                "message": message,
                "insertions": insertions,
                "deletions": deletions,
                "total": total,
                "day_key": day_key
            }

            if len(commits) < 3:
                log(f"处理提交: {message[:70]}... - 增:{insertions} 删:{deletions} 总:{total} - 日期键:{day_key}")
            commits.append(commit)

        return commits
    except subprocess.CalledProcessError as e:
        log(f"执行git命令失败: {e.stderr}", "ERROR")
        sys.exit(1)
    except Exception as e:
        log(f"发生未知错误: {e}", "ERROR")
        sys.exit(1)

def filter_commits(commits, min_changes=10):
    """过滤掉修改行数少于指定数量的提交"""
    filtered = [c for c in commits if c['total'] >= min_changes]
    log(f"过滤后: 从 {len(commits)} 条减少到 {len(filtered)} 条")
    return filtered

def analyze_daily_changes(commits):
    """按日统计增加行数、删除行数、提交次数和时间"""
    daily_stats = {}

    for commit in commits:
        day_key = commit['day_key']
        date_obj = commit['date_obj']

        if day_key not in daily_stats:
            daily_stats[day_key] = {
                'date': day_key,
                'insertions': 0,
                'deletions': 0,
                'modified_lines': 0,  # 新增字段：修改行数（增加+删除）
                'commit_count': 0,
                'first_time': date_obj,
                'last_time': date_obj
            }

        daily_stats[day_key]['insertions'] += commit['insertions']
        daily_stats[day_key]['deletions'] += commit['deletions']
        daily_stats[day_key]['modified_lines'] += commit['total']  # 修改行数 = 增加 + 删除
        daily_stats[day_key]['commit_count'] += 1

        # 更新最早和最晚时间
        if date_obj < daily_stats[day_key]['first_time']:
            daily_stats[day_key]['first_time'] = date_obj
        if date_obj > daily_stats[day_key]['last_time']:
            daily_stats[day_key]['last_time'] = date_obj

    # 转换为列表并排序
    result = []
    for day, stats in daily_stats.items():
        result.append({
            'date': day,
            'insertions': stats['insertions'],
            'deletions': stats['deletions'],
            'modified_lines': stats['modified_lines'],  # 新增字段
            'commit_count': stats['commit_count'],
            'first_time': stats['first_time'].strftime('%H:%M'),
            'last_time': stats['last_time'].strftime('%H:%M')
        })

    # 按日期排序
    result.sort(key=lambda x: x['date'])
    log(f"按日统计完成，共有 {len(result)} 天有有效提交")
    return result

def write_to_csv(data, filename="1.csv", headers=None):
    """将数据写入CSV文件(ANSI编码)"""
    log(f"开始将 {len(data)} 条记录写入CSV文件: {filename}")
    try:
        with open(filename, 'w', newline='', encoding='ansi') as csvfile:
            if not headers and data:
                headers = list(data[0].keys())

            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()

            for row in data:
                writer.writerow(row)

        log(f"成功写入CSV文件，路径: {os.path.abspath(filename)}")
        print(f"已将详细数据导出到: {filename}")
    except Exception as e:
        log(f"写入CSV文件失败: {e}", "ERROR")
        print(f"警告: 无法导出数据到 {filename} - {e}", file=sys.stderr)

def format_table(data, headers):
    """将数据格式化为控制台表格"""
    if not data:
        return "没有数据可显示"

    # 计算每列的最大宽度
    all_rows = [headers] + [list(row.values()) for row in data]
    col_widths = [
        max(len(str(row[i])) for row in all_rows)
        for i in range(len(headers))
    ]

    # 生成表头分隔线
    separator = "-" * (sum(col_widths) + 3 * (len(headers) - 1))

    # 格式化表头
    header_row = "  ".join(
        str(headers[i]).ljust(col_widths[i])
        for i in range(len(headers))
    )

    # 格式化数据行
    data_rows = []
    for row in data:
        data_rows.append(
            "  ".join(
                str(list(row.values())[i]).ljust(col_widths[i])
                for i in range(len(row))
            )
        )

    return f"{header_row}\n{separator}\n" + "\n".join(data_rows)

def main():
    log("=== 开始执行git提交记录分析脚本 ===")
    log(f"Python版本: {sys.version}")
    log(f"当前工作目录: {os.getcwd()}")

    set_utf8_output()

    author = "Briley ho"
    min_changes = 1

    print(f"正在分析 {author} 的非merge提交记录...")
    log(f"目标作者: {author}")
    log(f"最小修改行数过滤: {min_changes}")

    # 获取所有提交
    commits = get_git_commits(author)

    if not commits:
        print(f"未找到 {author} 的非merge提交记录")
        log("程序正常结束: 没有找到提交记录")
        return

    # 过滤提交
    filtered_commits = filter_commits(commits, min_changes)

    if not filtered_commits:
        print(f"没有找到修改行数大于等于 {min_changes} 的提交记录")
        log("程序正常结束: 没有符合条件的提交记录")
        return

    # 按日分析
    daily_stats = analyze_daily_changes(filtered_commits)

    # 导出详细数据
    write_to_csv(
        filtered_commits,
        "commits.csv",
        ["day_key", "date_str", "author", "message", "insertions", "deletions", "total"]
    )

    # 导出每日统计 (调整了列顺序以匹配需求)
    write_to_csv(
        daily_stats,
        "daily_stats.csv",
        ["date", "insertions", "deletions", "modified_lines", "commit_count", "first_time", "last_time"]
    )

    # 显示每日统计表格
    print("\n=== 每日代码改动统计 ===")
    print(format_table(
        daily_stats,
        ["日期", "增加行数", "删除行数", "修改行数", "提交次数", "最早提交时间", "最晚提交时间"]
    ))

    total_days = len(daily_stats)
    total_commits = len(filtered_commits)

    # 计算总计
    total_insertions = sum(d['insertions'] for d in daily_stats)
    total_deletions = sum(d['deletions'] for d in daily_stats)
    total_modified = sum(d['modified_lines'] for d in daily_stats)

    print(f"\n分析完成:")
    print(f"- 共分析 {total_days} 天")
    print(f"- 共 {total_commits} 条符合条件的提交记录")
    print(f"- 总计增加 {total_insertions} 行代码")
    print(f"- 总计删除 {total_deletions} 行代码")
    print(f"- 总计修改 {total_modified} 行代码")
    print(f"- 所有数据已导出到 commits.csv 和 daily_stats.csv")

    log(f"程序成功完成，共分析 {total_days} 天，{total_commits} 条提交记录")
    log("=== 脚本执行完毕 ===")

if __name__ == "__main__":
    main()